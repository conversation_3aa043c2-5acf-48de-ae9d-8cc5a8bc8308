# Backend Invoice API

A RESTful API for managing invoices built with Node.js, Express, and MongoDB.

## Features

- CRUD operations for invoices
- MongoDB integration with Mongoose
- Input validation
- Error handling
- Auto-calculation of invoice totals
- RESTful API design

## Prerequisites

- Node.js (v14 or higher)
- MongoDB (local installation or MongoDB Atlas)
- npm or yarn

## Installation

1. Clone the repository:
```bash
git clone https://github.com/Nawinkishore/Backend-invoice.git
cd Backend-invoice
```

2. Install dependencies:
```bash
npm install
```

3. Create a `.env` file in the root directory and add your MongoDB connection string:
```env
NODE_ENV=development
PORT=5000
MONGODB_URI=your_mongodb_connection_string_here
JWT_SECRET=your-super-secret-jwt-key-here
API_VERSION=v1
```

4. Start the development server:
```bash
npm run dev
```

The server will start on `http://localhost:5000`

## Scripts

- `npm start` - Start the production server
- `npm run dev` - Start the development server with nodemon
- `npm test` - Run tests (to be implemented)

## API Endpoints

### Base URL: `http://localhost:5000/api`

### Invoices

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/invoices` | Get all invoices |
| GET | `/invoices/:id` | Get a specific invoice |
| POST | `/invoices` | Create a new invoice |
| PUT | `/invoices/:id` | Update an invoice |
| DELETE | `/invoices/:id` | Delete an invoice |

### Health Check

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/health` | Check API health status |

## Invoice Schema

```json
{
  "invoiceNumber": "String (required, unique)",
  "clientName": "String (required)",
  "clientEmail": "String (required, valid email)",
  "clientAddress": {
    "street": "String",
    "city": "String",
    "state": "String",
    "zipCode": "String",
    "country": "String"
  },
  "items": [
    {
      "description": "String (required)",
      "quantity": "Number (required, min: 1)",
      "unitPrice": "Number (required, min: 0)",
      "total": "Number (auto-calculated)"
    }
  ],
  "subtotal": "Number (auto-calculated)",
  "taxRate": "Number (default: 0, max: 100)",
  "taxAmount": "Number (auto-calculated)",
  "total": "Number (auto-calculated)",
  "status": "String (enum: draft, sent, paid, overdue, cancelled)",
  "issueDate": "Date (default: now)",
  "dueDate": "Date (required)",
  "notes": "String"
}
```

## Example Usage

### Create an Invoice

```bash
curl -X POST http://localhost:5000/api/invoices \
  -H "Content-Type: application/json" \
  -d '{
    "clientName": "John Doe",
    "clientEmail": "<EMAIL>",
    "items": [
      {
        "description": "Web Development",
        "quantity": 10,
        "unitPrice": 100
      }
    ],
    "taxRate": 10,
    "dueDate": "2024-02-01"
  }'
```

## Project Structure

```
├── config/
│   └── database.js          # Database configuration
├── controllers/
│   └── invoiceController.js # Invoice controller
├── models/
│   └── Invoice.js           # Invoice model
├── routes/
│   ├── index.js            # Main routes
│   └── invoices.js         # Invoice routes
├── middleware/             # Custom middleware (future)
├── utils/                  # Utility functions (future)
├── .env                    # Environment variables
├── .gitignore             # Git ignore file
├── package.json           # Dependencies and scripts
├── server.js              # Main server file
└── README.md              # This file
```

## Environment Variables

- `NODE_ENV` - Environment (development/production)
- `PORT` - Server port (default: 5000)
- `MONGODB_URI` - MongoDB connection string
- `JWT_SECRET` - Secret key for JWT tokens (for future auth)
- `API_VERSION` - API version

## Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## License

This project is licensed under the ISC License.
