<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle
} from '@/components/ui/card'
import {
  Ta<PERSON>,
  Ta<PERSON>Content,
  TabsList,
  TabsTrigger
} from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { clientApi, type Client, type ClientDetailedStatsResponse } from '~/services/clientApi'

// Props
interface Props {
  clientId: string
}

const props = defineProps<Props>()

// Reactive state
const loading = ref(true)
const error = ref<string | null>(null)
const client = ref<Client | null>(null)
const clientStats = ref<ClientDetailedStatsResponse['data']['invoiceStats'] | null>(null)
const recentActivity = ref<ClientDetailedStatsResponse['data']['recentActivity']>([])

const tabs = [
  { name: 'Overview', key: 'overview' },
  { name: 'Invoices', key: 'invoices' },
  { name: 'Analytics', key: 'analytics' },
  { name: 'Documents', key: 'documents' }
]

const selectedTab = ref('overview')

// Computed properties
const clientInitial = computed(() => {
  if (!client.value) return 'C'
  const name = client.value.company?.name || client.value.name
  return name?.[0]?.toUpperCase() || 'C'
})

const companyName = computed(() => {
  if (!client.value) return ''
  return client.value.company?.name || client.value.name || ''
})

const clientType = computed(() => {
  if (!client.value) return 'Individual'
  return client.value.type || 'Individual'
})

const gstNumber = computed(() => {
  if (!client.value) return ''
  return client.value.company?.taxId || ''
})

const createdDate = computed(() => {
  if (!client.value?.createdAt) return 'N/A'
  return new Date(client.value.createdAt).toLocaleDateString('en-US')
})

const websiteUrl = computed(() => {
  if (!client.value?.company?.website) return ''
  return client.value.company.website
})

const contactPerson = computed(() => {
  if (!client.value?.company?.contactPerson) return ''
  return client.value.company.contactPerson
})

const fullAddress = computed(() => {
  if (!client.value?.address) return ''
  const addr = client.value.address
  const parts = [
    addr.street,
    addr.city,
    addr.state,
    addr.zipCode,
    addr.country
  ].filter(Boolean)
  return parts.join(', ')
})

const currencySymbol = computed(() => {
  const currency = client.value?.currency || 'INR'
  const symbols: Record<string, string> = {
    'INR': '₹',
    'USD': '$',
    'EUR': '€',
    'GBP': '£',
    'CAD': 'C$',
    'AUD': 'A$',
    'JPY': '¥'
  }
  return symbols[currency] || '₹'
})

const paymentTermsDisplay = computed(() => {
  return client.value?.paymentTerms || 'Net 30'
})

const clientCurrency = computed(() => {
  return client.value?.currency || 'INR'
})

const clientStatus = computed(() => {
  return client.value?.status || 'Active'
})

// Helper function to format numbers with commas
const formatNumber = (num: number | undefined | null): string => {
  if (!num) return '0'
  return num.toLocaleString('en-IN')
}

const clientNotes = computed(() => {
  return client.value?.notes || 'No notes available'
})

// Data fetching functions
const fetchClientData = async () => {
  if (!props.clientId) {
    error.value = 'No client ID provided'
    loading.value = false
    return
  }

  try {
    loading.value = true
    error.value = null

    console.log('🔄 Fetching client data for ID:', props.clientId)

    // Fetch client details and statistics in parallel
    const [clientResponse, statsResponse] = await Promise.all([
      clientApi.getClient(props.clientId),
      clientApi.getClientDetailedStats(props.clientId)
    ])

    if (clientResponse.success && clientResponse.data) {
      client.value = clientResponse.data
      console.log('✅ Client data loaded:', client.value)
    } else {
      throw new Error(clientResponse.message || 'Failed to fetch client data')
    }

    if (statsResponse.success && statsResponse.data) {
      clientStats.value = statsResponse.data.invoiceStats
      recentActivity.value = statsResponse.data.recentActivity
      console.log('✅ Client stats loaded:', clientStats.value)
    } else {
      console.warn('⚠️ Failed to fetch client stats')
      // Don't throw error for stats as client data is more important
    }
  } catch (err: any) {
    console.error('❌ Error fetching client data:', err)
    error.value = err.message || 'Failed to load client data'
  } finally {
    loading.value = false
  }
}

// Watch for clientId changes
watch(() => props.clientId, async (newId: string) => {
  if (newId) {
    await fetchClientData()
  }
}, { immediate: true })

// Initialize data on mount
onMounted(async () => {
  await fetchClientData()
})
</script>

<template>
  <Heading
      title="Manage Clients"
      description="Manage your clients and their details"
      icon="lucide:users"
      iconColor="text-green-400"
      bgColor="bg-white"
      :buttons="[
        {
          label: 'Back to Clients',
          icon: 'lucide:arrow-left',
          bgColor: 'bg-[#00C951]',
          textColor: 'text-white',
          to: '/clients'
        },
      ]"
    />
  <!-- Loading State -->
  <div v-if="loading" class="flex items-center justify-center py-12">
    <Icon name="lucide:loader-2" class="w-8 h-8 animate-spin text-[#00C951]" />
    <span class="ml-2 text-gray-600">Loading client details...</span>
  </div>

  <!-- Error State -->
  <div v-else-if="error" class="text-center py-12">
    <Icon name="lucide:alert-circle" class="w-12 h-12 mx-auto mb-4 text-red-500" />
    <h2 class="text-xl font-semibold text-gray-900 mb-2">Error Loading Client</h2>
    <p class="text-gray-600 mb-4">{{ error }}</p>
    <Button @click="fetchClientData" variant="outline">
      <Icon name="lucide:refresh-cw" class="w-4 h-4 mr-2" />
      Try Again
    </Button>
  </div>

  <!-- Client Details -->
  <div v-else-if="client" class="p-6 space-y-6 max-w-5xl mx-auto">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div class="flex items-center gap-4">
        <div
          class="w-14 h-14 rounded-full bg-gradient-to-br from-purple-500 to-indigo-500 flex items-center justify-center text-white text-xl font-bold"
        >
          {{ clientInitial }}
        </div>
        <div>
          <h2 class="text-xl font-semibold">{{ companyName }}</h2>
          <div class="flex items-center gap-2 mt-1">
            <Badge variant="secondary">{{ clientType }}</Badge>
            <Badge :variant="clientStatus === 'Active' ? 'default' : 'secondary'">{{ clientStatus }}</Badge>
          </div>
        </div>
      </div>
      <Button variant="outline">Edit</Button>
    </div>

    <!-- Tabs -->
    <Tabs v-model="selectedTab" class="w-full">
      <TabsList class="grid grid-cols-4 w-full border-b mb-4">
        <TabsTrigger
          v-for="tab in tabs"
          :key="tab.key"
          :value="tab.key"
          class="py-2 px-4 text-sm font-medium"
        >
          {{ tab.name }}
        </TabsTrigger>
      </TabsList>

      <!-- Overview Tab -->
      <TabsContent value="overview">
        <div class="space-y-6">
          <!-- Stats -->
          <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
            <Card class="bg-blue-50 text-center">
              <CardContent>
                <p class="text-sm text-gray-500">Total Invoices</p>
                <p class="text-2xl font-bold">{{ clientStats?.totalInvoices || 0 }}</p>
              </CardContent>
            </Card>
            <Card class="bg-green-50 text-center">
              <CardContent>
                <p class="text-sm text-gray-500">Paid</p>
                <p class="text-2xl font-bold">{{ clientStats?.paidInvoices || 0 }}</p>
              </CardContent>
            </Card>
            <Card class="bg-yellow-50 text-center">
              <CardContent>
                <p class="text-sm text-gray-500">Pending</p>
                <p class="text-2xl font-bold">{{ clientStats?.pendingInvoices || 0 }}</p>
              </CardContent>
            </Card>
            <Card class="bg-red-50 text-center">
              <CardContent>
                <p class="text-sm text-gray-500">Overdue</p>
                <p class="text-2xl font-bold">{{ clientStats?.overdueInvoices || 0 }}</p>
              </CardContent>
            </Card>
            <Card class="bg-orange-50 text-center">
              <CardContent>
                <p class="text-sm text-gray-500">Total Due</p>
                <p class="text-2xl font-bold text-orange-600">{{ currencySymbol }}{{ formatNumber(clientStats?.totalOutstanding) }}</p>
              </CardContent>
            </Card>
          </div>

          <!-- Info Sections -->
          <div class="grid md:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Contact Information</CardTitle>
              </CardHeader>
              <CardContent class="space-y-2">
                <p><strong>Email:</strong> {{ client.email || 'N/A' }}</p>
                <p><strong>Phone:</strong> {{ client.phone || 'N/A' }}</p>
                <p v-if="client.type === 'Business'"><strong>Company:</strong> {{ companyName || 'N/A' }}</p>
                <p v-if="client.type === 'Business'"><strong>Contact Person:</strong> {{ contactPerson || 'N/A' }}</p>
                <p v-if="client.type === 'Business'"><strong>Website:</strong>
                  <a v-if="websiteUrl" :href="websiteUrl" target="_blank" class="text-blue-600 hover:underline">{{ websiteUrl }}</a>
                  <span v-else>N/A</span>
                </p>
                <p v-if="gstNumber"><strong>GST:</strong> {{ gstNumber }}</p>
                <p v-if="fullAddress"><strong>Address:</strong> {{ fullAddress }}</p>
                <p><strong>Currency:</strong> {{ clientCurrency }}</p>
                <p><strong>Payment Terms:</strong> {{ paymentTermsDisplay }}</p>
                <p><strong>Status:</strong>
                  <Badge :variant="clientStatus === 'Active' ? 'default' : 'secondary'">{{ clientStatus }}</Badge>
                </p>
                <p><strong>Created:</strong> {{ createdDate }}</p>
                <div v-if="client.notes" class="mt-3 pt-3 border-t">
                  <p><strong>Notes:</strong></p>
                  <p class="text-sm text-gray-600 mt-1">{{ clientNotes }}</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Financial Summary</CardTitle>
              </CardHeader>
              <CardContent class="space-y-2">
                <p>Total Revenue: {{ currencySymbol }}{{ formatNumber(clientStats?.totalRevenue) }}</p>
                <p>Outstanding: <span class="text-red-600">{{ currencySymbol }}{{ formatNumber(clientStats?.totalOutstanding) }}</span></p>
                <p>Total Due: <span class="text-orange-600">{{ currencySymbol }}{{ formatNumber(client.totalAmount || clientStats?.totalOutstanding) }}</span></p>
                <p>Product Revenue: {{ currencySymbol }}{{ formatNumber(clientStats?.productRevenue) }}</p>
                <p>Service Revenue: {{ currencySymbol }}{{ formatNumber(clientStats?.serviceRevenue) }}</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent class="flex flex-col gap-2">
                <Button variant="outline">Create Invoice</Button>
                <Button variant="outline">Send Email</Button>
                <Button variant="outline">Export Data</Button>
                <Button variant="outline">Record Payment</Button>
              </CardContent>
            </Card>
          </div>

          <!-- Recent Activity -->
          <div>
            <h3 class="font-semibold text-lg">Recent Activity</h3>
            <div v-if="recentActivity.length > 0" class="mt-4 space-y-3">
              <div
                v-for="(activity, index) in recentActivity"
                :key="index"
                class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
              >
                <div>
                  <p class="text-sm font-medium text-gray-900">{{ activity.description }}</p>
                  <p class="text-xs text-gray-500">{{ new Date(activity.date).toLocaleDateString() }}</p>
                </div>
                <div v-if="activity.amount" class="text-sm font-semibold text-gray-900">
                  {{ currencySymbol }}{{ formatNumber(activity.amount) }}
                </div>
              </div>
            </div>
            <p v-else class="mt-2 text-gray-500">No recent activity.</p>
          </div>
        </div>
      </TabsContent>

      <!-- Other Tabs -->
      <TabsContent
        v-for="tab in tabs.filter(t => t.key !== 'overview')"
        :key="tab.key"
        :value="tab.key"
      >
        <Card>
          <CardContent class="py-6 text-center text-gray-500">
            {{ tab.name }} content goes here...
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  </div>
</template>
