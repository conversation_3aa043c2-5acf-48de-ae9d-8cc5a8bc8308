// Script to update existing clients with missing clientId fields
const mongoose = require('mongoose');
require('dotenv').config();

// Import the Client model
const Client = require('../models/Client');

async function updateClientIds() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Find all clients without clientId
    const clientsWithoutId = await Client.find({ 
      $or: [
        { clientId: { $exists: false } },
        { clientId: null },
        { clientId: '' }
      ]
    });

    console.log(`Found ${clientsWithoutId.length} clients without clientId`);

    // Update each client
    for (const client of clientsWithoutId) {
      console.log(`Updating client: ${client.name} (${client.email})`);
      
      // Trigger the pre-save middleware to generate clientId
      await client.save();
      
      console.log(`✅ Updated client with ID: ${client.clientId}`);
    }

    console.log('✅ All clients updated successfully');
    
  } catch (error) {
    console.error('❌ Error updating clients:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

// Run the script
updateClientIds();
