import type { Invoice } from '~/services/invoiceApi'

export interface PrintOptions {
  title?: string
  removeAfterPrint?: boolean
  printDelay?: number
}

export class InvoicePrintManager {
  private invoice: Invoice
  private options: PrintOptions

  constructor(invoice: Invoice, options: PrintOptions = {}) {
    this.invoice = invoice
    this.options = {
      title: `Invoice ${invoice.invoiceNumber}`,
      removeAfterPrint: true,
      printDelay: 500,
      ...options
    }
  }

  // Open print preview with optimized layout
  async openPrintPreview(): Promise<void> {
    try {
      // Create print-optimized content
      const printContent = this.generatePrintHTML()
      
      // Create new window for printing
      const printWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes')
      
      if (!printWindow) {
        throw new Error('Unable to open print window. Please check your popup blocker settings.')
      }

      // Write content to print window
      printWindow.document.write(printContent)
      printWindow.document.close()

      // Wait for content to load
      await this.waitForLoad(printWindow)

      // Focus and trigger print dialog
      printWindow.focus()
      
      // Add slight delay to ensure content is fully rendered
      setTimeout(() => {
        printWindow.print()
        
        // Close window after printing if specified
        if (this.options.removeAfterPrint) {
          setTimeout(() => {
            printWindow.close()
          }, 1000)
        }
      }, this.options.printDelay)

    } catch (error) {
      console.error('Error opening print preview:', error)
      throw new Error('Failed to open print preview. Please try again.')
    }
  }

  // Print current page with print-specific styles
  async printCurrentPage(): Promise<void> {
    try {
      // Add print styles to current page
      this.addPrintStyles()
      
      // Trigger print dialog
      window.print()
      
      // Remove print styles after printing
      setTimeout(() => {
        this.removePrintStyles()
      }, 1000)

    } catch (error) {
      console.error('Error printing current page:', error)
      throw new Error('Failed to print page. Please try again.')
    }
  }

  private generatePrintHTML(): string {
    return `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${this.options.title}</title>
        <style>
          ${this.getPrintStyles()}
        </style>
      </head>
      <body>
        <div class="print-container">
          ${this.generateInvoiceContent()}
        </div>
        <script>
          // Auto-focus for better print experience
          window.onload = function() {
            window.focus();
          };
          
          // Handle print completion
          window.onafterprint = function() {
            console.log('Print completed');
          };
        </script>
      </body>
      </html>
    `
  }

  private getPrintStyles(): string {
    return `
      /* Reset and base styles */
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: 'Arial', 'Helvetica', sans-serif;
        font-size: 12px;
        line-height: 1.5;
        color: #000;
        background: white;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
      }

      .print-container {
        max-width: 100%;
        margin: 0;
        padding: 20px;
        background: white;
      }

      /* Header styles */
      .invoice-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 30px;
        padding-bottom: 15px;
        border-bottom: 2px solid #000;
      }

      .company-info h1 {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 5px;
      }

      .invoice-title h2 {
        font-size: 32px;
        font-weight: bold;
        margin-bottom: 5px;
      }

      .invoice-number {
        font-size: 14px;
        font-weight: 500;
      }

      /* Details section */
      .invoice-details {
        display: flex;
        justify-content: space-between;
        margin-bottom: 30px;
      }

      .bill-to, .invoice-info {
        flex: 1;
      }

      .bill-to {
        margin-right: 30px;
      }

      .section-title {
        font-size: 12px;
        font-weight: bold;
        margin-bottom: 10px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      .client-name {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 5px;
      }

      .detail-row {
        margin-bottom: 3px;
      }

      /* Status badges */
      .status-badges {
        margin-top: 10px;
      }

      .status-badge {
        display: inline-block;
        padding: 2px 8px;
        border: 1px solid #000;
        border-radius: 3px;
        font-size: 10px;
        font-weight: bold;
        text-transform: uppercase;
        margin-right: 5px;
      }

      /* Items table */
      .items-table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 20px;
      }

      .items-table th,
      .items-table td {
        padding: 8px;
        text-align: left;
        border-bottom: 1px solid #000;
      }

      .items-table th {
        background-color: #f5f5f5;
        font-weight: bold;
        font-size: 11px;
        text-transform: uppercase;
      }

      .items-table th:last-child,
      .items-table td:last-child {
        text-align: right;
      }

      .item-description {
        font-weight: 500;
      }

      .item-type {
        font-size: 9px;
        color: #666;
        font-style: italic;
      }

      /* Totals section */
      .totals-section {
        display: flex;
        justify-content: flex-end;
        margin-bottom: 30px;
      }

      .totals-table {
        width: 250px;
      }

      .totals-row {
        display: flex;
        justify-content: space-between;
        padding: 5px 0;
        border-bottom: 1px solid #ccc;
      }

      .totals-row.total-final {
        border-top: 2px solid #000;
        border-bottom: 2px solid #000;
        font-weight: bold;
        font-size: 14px;
        padding: 10px 0;
        margin-top: 5px;
      }

      /* Notes section */
      .notes-section {
        margin-top: 30px;
        padding-top: 15px;
        border-top: 1px solid #ccc;
      }

      .notes-content {
        line-height: 1.6;
        white-space: pre-wrap;
      }

      /* Footer */
      .print-footer {
        margin-top: 40px;
        padding-top: 15px;
        border-top: 1px solid #ccc;
        text-align: center;
        font-size: 10px;
        color: #666;
      }

      /* Print-specific styles */
      @media print {
        @page {
          margin: 0.5in;
          size: A4 portrait;
        }

        body {
          font-size: 11px;
        }

        .print-container {
          padding: 0;
        }

        .invoice-header {
          margin-bottom: 20px;
        }

        .invoice-details {
          margin-bottom: 20px;
        }

        .items-table {
          margin-bottom: 15px;
        }

        .totals-section {
          margin-bottom: 20px;
        }

        /* Ensure no page breaks in critical sections */
        .invoice-header,
        .totals-section {
          page-break-inside: avoid;
        }

        /* Hide elements that shouldn't print */
        .no-print {
          display: none !important;
        }
      }

      /* Screen-only styles */
      @media screen {
        .print-container {
          box-shadow: 0 0 10px rgba(0,0,0,0.1);
          margin: 20px auto;
          max-width: 800px;
        }
      }
    `
  }

  private generateInvoiceContent(): string {
    const { invoice } = this
    
    return `
      <div class="invoice-header">
        <div class="company-info">
          <h1>Invoice @Easy</h1>
          <p>Professional Invoice Management</p>
        </div>
        <div class="invoice-title">
          <h2>INVOICE</h2>
          <div class="invoice-number">${invoice.invoiceNumber}</div>
        </div>
      </div>

      <div class="invoice-details">
        <div class="bill-to">
          <div class="section-title">Bill To</div>
          <div class="client-name">${invoice.clientName}</div>
          <div class="detail-row">${invoice.clientEmail}</div>
          ${invoice.clientPhone ? `<div class="detail-row">${invoice.clientPhone}</div>` : ''}
          ${this.generateClientAddress()}
        </div>
        <div class="invoice-info">
          <div class="section-title">Invoice Details</div>
          <div class="detail-row"><strong>Issue Date:</strong> ${this.formatDate(invoice.issueDate)}</div>
          <div class="detail-row"><strong>Due Date:</strong> ${this.formatDate(invoice.dueDate)}</div>
          <div class="detail-row"><strong>Type:</strong> ${invoice.invoiceType.charAt(0).toUpperCase() + invoice.invoiceType.slice(1)}</div>
          ${invoice.paymentTerms ? `<div class="detail-row"><strong>Payment Terms:</strong> ${invoice.paymentTerms}</div>` : ''}
          <div class="status-badges">
            ${invoice.isPaid ? '<span class="status-badge">PAID</span>' : ''}
            ${this.isOverdue() ? '<span class="status-badge">OVERDUE</span>' : ''}
          </div>
        </div>
      </div>

      ${this.generateItemsTable()}
      ${this.generateTotals()}
      ${this.generateNotes()}
      
      <div class="print-footer">
        <p>Generated on ${this.formatDate(new Date().toISOString())} | Invoice @Easy</p>
      </div>
    `
  }

  private generateClientAddress(): string {
    const address = this.invoice.clientAddress
    if (!address) return ''

    let addressHTML = ''
    if (address.street) addressHTML += `<div class="detail-row">${address.street}</div>`
    
    const cityStateZip = [address.city, address.state, address.zipCode].filter(Boolean).join(', ')
    if (cityStateZip) addressHTML += `<div class="detail-row">${cityStateZip}</div>`
    
    if (address.country) addressHTML += `<div class="detail-row">${address.country}</div>`
    
    return addressHTML
  }

  private generateItemsTable(): string {
    const items = this.invoice.items.map(item => `
      <tr>
        <td>
          <div class="item-description">${item.description}</div>
          ${item.type ? `<div class="item-type">${item.type}</div>` : ''}
        </td>
        <td style="text-align: center;">${item.quantity}</td>
        <td style="text-align: center;">₹${this.formatCurrency(item.unitPrice)}</td>
        <td>₹${this.formatCurrency(item.total || (item.quantity * item.unitPrice))}</td>
      </tr>
    `).join('')

    return `
      <table class="items-table">
        <thead>
          <tr>
            <th>Description</th>
            <th style="text-align: center;">Qty</th>
            <th style="text-align: center;">Unit Price</th>
            <th>Total</th>
          </tr>
        </thead>
        <tbody>
          ${items}
        </tbody>
      </table>
    `
  }

  private generateTotals(): string {
    const { invoice } = this
    
    return `
      <div class="totals-section">
        <div class="totals-table">
          <div class="totals-row">
            <span>Subtotal:</span>
            <span>₹${this.formatCurrency(invoice.subtotal)}</span>
          </div>
          ${invoice.discountRate > 0 ? `
            <div class="totals-row">
              <span>Discount (${invoice.discountRate}%):</span>
              <span>-₹${this.formatCurrency(invoice.discountAmount)}</span>
            </div>
          ` : ''}
          ${invoice.taxRate > 0 ? `
            <div class="totals-row">
              <span>Tax (${invoice.taxRate}%):</span>
              <span>₹${this.formatCurrency(invoice.taxAmount)}</span>
            </div>
          ` : ''}
          <div class="totals-row total-final">
            <span>TOTAL:</span>
            <span>₹${this.formatCurrency(invoice.total)}</span>
          </div>
        </div>
      </div>
    `
  }

  private generateNotes(): string {
    if (!this.invoice.notes) return ''
    
    return `
      <div class="notes-section">
        <div class="section-title">Notes</div>
        <div class="notes-content">${this.invoice.notes}</div>
      </div>
    `
  }

  private addPrintStyles(): void {
    const styleId = 'invoice-print-styles'
    
    // Remove existing print styles
    const existingStyles = document.getElementById(styleId)
    if (existingStyles) {
      existingStyles.remove()
    }

    // Add new print styles
    const style = document.createElement('style')
    style.id = styleId
    style.textContent = `
      @media print {
        body * {
          visibility: hidden;
        }
        
        .invoice-print-area,
        .invoice-print-area * {
          visibility: visible;
        }
        
        .invoice-print-area {
          position: absolute;
          left: 0;
          top: 0;
          width: 100%;
        }
        
        .no-print {
          display: none !important;
        }
      }
    `
    
    document.head.appendChild(style)
  }

  private removePrintStyles(): void {
    const styleElement = document.getElementById('invoice-print-styles')
    if (styleElement) {
      styleElement.remove()
    }
  }

  private async waitForLoad(window: Window): Promise<void> {
    return new Promise((resolve) => {
      if (window.document.readyState === 'complete') {
        resolve()
      } else {
        window.onload = () => resolve()
        // Fallback timeout
        setTimeout(resolve, 1000)
      }
    })
  }

  private formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  private formatCurrency(amount: number): string {
    return amount.toFixed(2)
  }

  private isOverdue(): boolean {
    if (this.invoice.isPaid) return false
    const currentDate = new Date()
    const dueDate = new Date(this.invoice.dueDate)
    return currentDate > dueDate
  }
}

// Utility functions for easy use
export const printInvoice = async (invoice: Invoice, options?: PrintOptions): Promise<void> => {
  const printManager = new InvoicePrintManager(invoice, options)
  await printManager.openPrintPreview()
}

export const printCurrentPage = async (invoice: Invoice): Promise<void> => {
  const printManager = new InvoicePrintManager(invoice)
  await printManager.printCurrentPage()
}

export default InvoicePrintManager
