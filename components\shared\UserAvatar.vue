<template>
    <div class="flex flex-col items-center gap-4" v-if="user">
      <Avatar v-if="user.user_metadata.avatar_url">
        <AvatarImage :src="user.user_metadata.avatar_url" />
        <AvatarFallback>
          {{ user.email.charAt(0).toUpperCase() }}
          {{ user.email.charAt(1).toUpperCase() }}
        </AvatarFallback>
      </Avatar>
  
      <Avatar v-else-if="user.user_metadata.full_name">
        <AvatarFallback>
          {{
            user.user_metadata.full_name.split(' ')[0].charAt(0).toUpperCase()
          }}
          {{
            user.user_metadata.full_name.split(' ')[1]?.charAt(0).toUpperCase() ?? ''
          }}
        </AvatarFallback>
      </Avatar>
  
      <Avatar v-else>
        <AvatarFallback>
          {{ user.email.charAt(0).toUpperCase() }}
          {{ user.email.charAt(1).toUpperCase() }}
        </AvatarFallback>
      </Avatar>
    </div>
  </template>
  
  <script lang="ts" setup>
  import { ref, onMounted } from 'vue'
  
  const user = ref<any>(null)
  
  onMounted(async () => {
    const res = await fetch('https://randomuser.me/api/')
    const data = await res.json()
    const u = data.results[0]
    user.value = {
      email: u.email,
      user_metadata: {
        full_name: `${u.name.first} ${u.name.last}`,
        avatar_url: u.picture.large
      }
    }
  })
  </script>
  
  <style scoped>
  </style>
  