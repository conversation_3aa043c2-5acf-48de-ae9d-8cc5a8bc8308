const mongoose = require('mongoose');

const ClientSchema = new mongoose.Schema({
  // User who owns this client (from Supabase)
  userId: {
    type: String,
    required: [true, 'User ID is required'],
    index: true
  },

  // Auto-generated client ID (e.g., "JOHN-DOE-001")
  clientId: {
    type: String,
    unique: true,
    index: true
  },
  
  // Client type
  type: {
    type: String,
    enum: ['Individual', 'Business'],
    default: 'Individual',
    required: [true, 'Client type is required']
  },

  // Basic client information
  name: {
    type: String,
    required: [true, 'Client name is required'],
    trim: true,
    maxlength: [100, 'Client name cannot exceed 100 characters']
  },

  email: {
    type: String,
    required: [true, 'Email is required'],
    trim: true,
    lowercase: true,
    match: [
      /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
      'Please enter a valid email address'
    ]
  },
  
  phone: {
    type: String,
    trim: true,
    match: [
      /^[\+]?[\d\s\-\(\)]{7,20}$/,
      'Please enter a valid phone number'
    ]
  },
  
  // Company information
  company: {
    name: {
      type: String,
      trim: true,
      maxlength: [100, 'Company name cannot exceed 100 characters']
    },
    website: {
      type: String,
      trim: true,
      match: [
        /^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/,
        'Please enter a valid website URL'
      ]
    },
    taxId: {
      type: String,
      trim: true,
      maxlength: [50, 'Tax ID cannot exceed 50 characters']
    },
    contactPerson: {
      type: String,
      trim: true,
      maxlength: [100, 'Contact person name cannot exceed 100 characters']
    }
  },
  
  // Address information
  address: {
    street: {
      type: String,
      trim: true,
      maxlength: [200, 'Street address cannot exceed 200 characters']
    },
    city: {
      type: String,
      trim: true,
      maxlength: [50, 'City cannot exceed 50 characters']
    },
    state: {
      type: String,
      trim: true,
      maxlength: [50, 'State cannot exceed 50 characters']
    },
    zipCode: {
      type: String,
      trim: true,
      maxlength: [20, 'Zip code cannot exceed 20 characters']
    },
    country: {
      type: String,
      trim: true,
      maxlength: [50, 'Country cannot exceed 50 characters'],
      default: 'India'
    }
  },
  
  // Financial information
  currency: {
    type: String,
    default: 'INR',
    enum: ['USD', 'EUR', 'GBP', 'CAD', 'AUD', 'INR', 'JPY'],
    uppercase: true
  },
  
  paymentTerms: {
    type: String,
    enum: ['Net 15', 'Net 30', 'Net 45', 'Net 60', 'Due on Receipt', 'Custom'],
    default: 'Net 30'
  },
  
  creditLimit: {
    type: Number,
    min: [0, 'Credit limit cannot be negative'],
    default: 0
  },
  
  // Status and metadata
  status: {
    type: String,
    enum: ['Active', 'Inactive', 'Suspended'],
    default: 'Active'
  },
  
  notes: {
    type: String,
    maxlength: [1000, 'Notes cannot exceed 1000 characters']
  },
  
  tags: [{
    type: String,
    trim: true,
    maxlength: [30, 'Tag cannot exceed 30 characters']
  }],
  
  // Invoice statistics (calculated fields)
  totalInvoices: {
    type: Number,
    default: 0,
    min: 0
  },
  
  totalAmount: {
    type: Number,
    default: 0,
    min: 0
  },
  
  outstandingAmount: {
    type: Number,
    default: 0,
    min: 0
  },
  
  lastInvoiceDate: {
    type: Date
  },
  
  // Timestamps
  createdAt: {
    type: Date,
    default: Date.now
  },
  
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true, // Automatically manage createdAt and updatedAt
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better query performance
ClientSchema.index({ userId: 1, email: 1 }, { unique: true }); // Unique email per user
ClientSchema.index({ userId: 1, name: 1 });
ClientSchema.index({ userId: 1, status: 1 });
ClientSchema.index({ userId: 1, createdAt: -1 });

// Virtual for full address
ClientSchema.virtual('fullAddress').get(function() {
  const addr = this.address;
  if (!addr.street && !addr.city && !addr.state && !addr.zipCode) {
    return null;
  }
  
  const parts = [
    addr.street,
    addr.city,
    addr.state,
    addr.zipCode,
    addr.country
  ].filter(Boolean);
  
  return parts.join(', ');
});

// Virtual for display name (company name or client name)
ClientSchema.virtual('displayName').get(function() {
  return this.company?.name || this.name;
});

// Pre-save middleware to generate client ID and update timestamps
ClientSchema.pre('save', async function(next) {
  // Generate client ID if it doesn't exist
  if (!this.clientId) {
    this.clientId = await generateClientId(this.name, this.userId);
  }

  this.updatedAt = new Date();
  next();
});

// Function to generate unique client ID
async function generateClientId(clientName, userId) {
  // Clean and format the name
  const cleanName = clientName
    .toUpperCase()
    .replace(/[^A-Z0-9\s]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .substring(0, 20); // Limit length

  // Generate a unique suffix
  let counter = 1;
  let clientId;
  let exists = true;

  while (exists) {
    const suffix = counter.toString().padStart(3, '0');
    clientId = `${cleanName}-${suffix}`;

    // Check if this ID already exists for this user
    const existingClient = await mongoose.model('Client').findOne({
      userId,
      clientId
    });

    if (!existingClient) {
      exists = false;
    } else {
      counter++;
    }
  }

  return clientId;
}

// Static method to find clients by user
ClientSchema.statics.findByUser = function(userId, options = {}) {
  const query = this.find({ userId });
  
  if (options.status) {
    query.where('status').equals(options.status);
  }
  
  if (options.search) {
    query.where({
      $or: [
        { name: { $regex: options.search, $options: 'i' } },
        { email: { $regex: options.search, $options: 'i' } },
        { 'company.name': { $regex: options.search, $options: 'i' } }
      ]
    });
  }
  
  if (options.sort) {
    query.sort(options.sort);
  } else {
    query.sort({ createdAt: -1 });
  }
  
  if (options.limit) {
    query.limit(options.limit);
  }
  
  if (options.skip) {
    query.skip(options.skip);
  }
  
  return query;
};

// Instance method to update invoice statistics
ClientSchema.methods.updateInvoiceStats = async function(invoiceData) {
  // This would be called when invoices are created/updated
  // Implementation depends on your invoice model structure
  this.totalInvoices = (this.totalInvoices || 0) + 1;
  this.totalAmount = (this.totalAmount || 0) + (invoiceData.total || 0);
  this.lastInvoiceDate = new Date();
  
  return this.save();
};

module.exports = mongoose.model('Client', ClientSchema);
