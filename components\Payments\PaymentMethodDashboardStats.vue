<template>
  <div class="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-6">
    <div class="p-4 bg-white rounded-lg shadow border flex items-center justify-between">
      <div>
        <p class="text-gray-500 text-sm">Total Methods</p>
        <h2 class="text-2xl font-bold">2</h2>
      </div>
      <Wallet class="w-6 h-6 text-blue-500" />
    </div>
    <div class="p-4 bg-white rounded-lg shadow border flex items-center justify-between">
      <div>
        <p class="text-gray-500 text-sm">Default Method</p>
        <h2 class="text-2xl font-bold">UPI</h2>
      </div>
      <ShieldCheck class="w-6 h-6 text-green-500" />
    </div>
    <div class="p-4 bg-white rounded-lg shadow border flex items-center justify-between">
      <div>
        <p class="text-gray-500 text-sm">Recently Added</p>
        <h2 class="text-2xl font-bold">0</h2>
      </div>
      <TrendingUp class="w-6 h-6 text-purple-500" />
    </div>
  </div>
</template>

<script setup>
import { Wallet, ShieldCheck, TrendingUp } from 'lucide-vue-next'
</script>
