const express = require('express');
const router = express.Router();
const {
  getInvoices,
  getInvoice,
  createInvoice,
  updateInvoice,
  deleteInvoice,
  getInvoiceStats,
  updatePaymentStatus,
  generateInvoicePDF,
  sendInvoiceEmail
} = require('../controllers/invoiceController');

// Import Supabase authentication middleware
const { verifySupabaseToken } = require('../middleware/supabaseAuth');

// Apply Supabase JWT authentication to all invoice routes
router.use(verifySupabaseToken);

// @route   GET /api/invoices/stats
// @desc    Get invoice statistics for authenticated user
// @access  Private (Supabase JWT required)
router.get('/stats', getInvoiceStats);

// @route   GET /api/invoices
// @desc    Get all invoices for authenticated user
// @access  Private (Supabase JWT required)
router.get('/', getInvoices);

// @route   GET /api/invoices/:id
// @desc    Get single invoice for authenticated user
// @access  Private (Supabase JWT required)
router.get('/:id', getInvoice);

// @route   POST /api/invoices
// @desc    Create new invoice for authenticated user
// @access  Private (Supabase JWT required)
router.post('/', createInvoice);

// @route   PUT /api/invoices/:id
// @desc    Update invoice for authenticated user
// @access  Private (Supabase JWT required)
router.put('/:id', updateInvoice);

// @route   DELETE /api/invoices/:id
// @desc    Delete invoice for authenticated user
// @access  Private (Supabase JWT required)
router.delete('/:id', deleteInvoice);

// @route   PATCH /api/invoices/:id/payment-status
// @desc    Update payment status of invoice for authenticated user
// @access  Private (Supabase JWT required)
router.patch('/:id/payment-status', updatePaymentStatus);

// @route   GET /api/invoices/:id/pdf
// @desc    Generate PDF for invoice for authenticated user
// @access  Private (Supabase JWT required)
router.get('/:id/pdf', generateInvoicePDF);

// @route   POST /api/invoices/:id/send-email
// @desc    Send invoice via email for authenticated user
// @access  Private (Supabase JWT required)
router.post('/:id/send-email', sendInvoiceEmail);

module.exports = router;
