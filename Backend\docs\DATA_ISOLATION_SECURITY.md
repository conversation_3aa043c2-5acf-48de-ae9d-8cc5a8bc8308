# Data Isolation & Security Documentation

## Overview

This document explains how the Invoice Easy application ensures complete data isolation between users, preventing any user from accessing another user's data.

## 🔒 Multi-Layer Security Architecture

### Layer 1: Authentication (Supabase JWT)
- **Purpose**: Verify user identity
- **Implementation**: `middleware/supabaseAuth.js`
- **Function**: Validates JWT tokens and extracts user ID
- **Protection**: Prevents unauthenticated access

### Layer 2: Database Schema Design
- **Purpose**: Enforce data ownership at database level
- **Implementation**: All models include `userId` field
- **Function**: Links every record to its owner
- **Protection**: Prevents data mixing at storage level

### Layer 3: Controller-Level Filtering
- **Purpose**: Filter data by user in business logic
- **Implementation**: All controllers use `userId` in queries
- **Function**: Ensures only user's data is returned/modified
- **Protection**: Prevents cross-user data access

### Layer 4: Enhanced Security Middleware
- **Purpose**: Additional validation and auditing
- **Implementation**: `middleware/dataIsolation.js`
- **Function**: Extra ownership checks and security auditing
- **Protection**: Defense in depth approach

## 🛡️ Data Isolation Implementation

### Database Models

All models include these security features:

```javascript
// Required userId field
userId: {
  type: String,
  required: [true, 'User ID is required'],
  index: true
}

// Compound unique indexes for user-scoped uniqueness
ClientSchema.index({ userId: 1, email: 1 }, { unique: true });
```

### Controller Security Patterns

Every controller follows this pattern:

```javascript
// 1. Extract user ID from authenticated request
const userId = req.user.id;

// 2. Filter queries by user ID
const clients = await Client.find({ userId });

// 3. Verify ownership for specific resources
const client = await Client.findOne({ _id: clientId, userId });

// 4. Add user ID to new records
const clientData = { ...req.body, userId };
```

### Route-Level Protection

Enhanced routes include multiple security layers:

```javascript
// Authentication + Data Isolation + Auditing
router.get('/:id', clientSecurity.ensureOwnership, getClient);
router.post('/', clientSecurity.createProtection, createClient);
router.put('/:id', clientSecurity.updateProtection, updateClient);
router.delete('/:id', clientSecurity.deleteProtection, deleteClient);
```

## 🔍 Security Features

### 1. Automatic User ID Assignment
- New records automatically get the authenticated user's ID
- Prevents manual user ID manipulation
- Ensures proper ownership from creation

### 2. Ownership Verification
- Every access to specific resources verifies ownership
- Returns 404 for non-existent or non-owned resources
- Prevents enumeration attacks

### 3. Query Filtering
- All list operations automatically filter by user ID
- No possibility of seeing other users' data
- Efficient database queries with proper indexing

### 4. Cross-User Access Prevention
- Middleware blocks attempts to access other users' data
- Validates resource ownership before any operation
- Prevents privilege escalation

### 5. Security Auditing
- All data access is logged with user ID, operation, and timestamp
- Enables security monitoring and forensics
- Helps detect suspicious activity

### 6. Input Validation
- Prevents user ID manipulation in request bodies
- Validates ObjectId formats
- Sanitizes user inputs

## 📊 Data Isolation by Module

### Clients
- ✅ Each client belongs to one user
- ✅ Email uniqueness enforced per user (not globally)
- ✅ Client IDs generated with user scope
- ✅ All operations filtered by user ID

### Quotes
- ✅ Each quote belongs to one user
- ✅ Can only reference user's own clients
- ✅ Quote numbers unique per user
- ✅ Conversion to invoices maintains ownership

### Expenses
- ✅ Each expense belongs to one user
- ✅ Approval workflow within user scope
- ✅ Categories and statistics per user
- ✅ Receipt management per user

### Products
- ✅ Each product belongs to one user
- ✅ Product IDs unique per user
- ✅ Inventory tracking per user
- ✅ Categories and pricing per user

### Services
- ✅ Each service belongs to one user
- ✅ Service IDs unique per user
- ✅ Availability settings per user
- ✅ Booking management per user

### Invoices
- ✅ Each invoice belongs to one user
- ✅ Can only reference user's own clients/quotes
- ✅ Invoice numbers unique per user
- ✅ Payment tracking per user

## 🧪 Testing Data Isolation

### Manual Testing Steps

1. **Create User A Account**
   - Register/login as User A
   - Create some clients, quotes, expenses

2. **Create User B Account**
   - Register/login as User B
   - Create different clients, quotes, expenses

3. **Verify Isolation**
   - User A should only see User A's data
   - User B should only see User B's data
   - No cross-contamination

### Automated Testing

Run the data isolation test:
```bash
cd Backend
node tests/dataIsolationTest.js
```

## 🚨 Security Alerts

### What to Monitor

1. **Failed Ownership Checks**
   - Users trying to access non-owned resources
   - Potential enumeration attacks

2. **User ID Manipulation Attempts**
   - Requests with modified user IDs
   - Potential privilege escalation attempts

3. **Bulk Operation Anomalies**
   - Large numbers of failed ownership checks
   - Potential automated attacks

### Log Examples

```
[AUDIT] 2024-01-15T10:30:00.000Z - User: user-123 - Operation: CREATE - Resource: client - IP: *************
[SECURITY] User user-456 attempted to access resource owned by user-123
[WARNING] Invalid ObjectId format in request from user-789
```

## ✅ Security Checklist

- [x] All models have required userId field
- [x] All controllers filter by userId
- [x] All routes have authentication
- [x] Ownership verification on resource access
- [x] Automatic userId assignment on creation
- [x] Cross-user access prevention
- [x] Security auditing enabled
- [x] Input validation implemented
- [x] Unique constraints scoped to users
- [x] Error messages don't leak information

## 🔧 Maintenance

### Regular Security Tasks

1. **Review Audit Logs**
   - Check for suspicious access patterns
   - Monitor failed ownership checks

2. **Update Dependencies**
   - Keep security packages updated
   - Monitor for vulnerabilities

3. **Test Data Isolation**
   - Run automated tests regularly
   - Perform manual verification

4. **Database Integrity**
   - Verify all records have userId
   - Check for orphaned data

## 📞 Security Contact

If you discover a security issue:
1. Do not create a public issue
2. Contact the development team directly
3. Provide detailed reproduction steps
4. Allow time for investigation and fix

## Conclusion

The Invoice Easy application implements comprehensive data isolation through multiple security layers. Users can be confident that their data is completely isolated from other users, with robust protections against unauthorized access, data leakage, and privilege escalation attacks.
