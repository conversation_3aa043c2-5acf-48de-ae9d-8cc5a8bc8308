<template>
  <div class="flex items-center justify-between p-2 py-5 font-serif bg-white border-b border-gray-200 sticky top-0 z-50 shadow-sm">
    <div class="flex items-center gap-3">
      <MobileSidebar />

      <div>
        <div
          class="flex gap-3 items-center border rounded-lg px-3 py-2 w-28 md:w-80 hover:w-56 hover:md:w-96 transition-all duration-300 ease-in-out bg-white shadow-sm focus-within:ring-2"
          style="--tw-ring-color: #05DF72"
          @click="open = true"
        >
          <Icon name="lucide:search" class="w-4 h-4 text-gray-400" />
          <span class="text-sm text-black/50">Type a command or search…</span>
        </div>

        <Dialog v-model:open="open">
          <DialogContent class="p-0 max-w-xl w-full rounded-xl shadow-xl bg-white text-black">
            <div
              class="flex gap-3 items-center border border-green-400 rounded-lg mt-4 ml-3 px-3 py-2 w-28 md:w-80 hover:w-56 hover:md:w-96 transition-all duration-300 ease-in-out bg-white shadow-sm focus-within:ring-2"
              style="--tw-ring-color: #05DF72"
            >
              <Icon name="lucide:search" class="w-4 h-4 text-gray-400" />
              <input
                v-model="searchQuery"
                placeholder="Type a command or search..."
                class="text-sm text-black placeholder:text-black/50 bg-transparent outline-none border-none ring-0 focus:ring-0 w-full"
                autofocus
              />
            </div>

            <div class="max-h-[400px] overflow-y-auto mt-2">
              <div v-if="filteredNavigation.length" class="px-4 pt-4 text-xs font-semibold text-green-400">
                <h1 class="ml-5 text-gray-400">Navigation</h1>
                <div
                  v-for="nav in filteredNavigation"
                  :key="nav.name"
                  @click="navigateTo(nav.route)"
                  class="flex items-center justify-between px-4 py-2 hover:bg-green-400/10 hover:text-green-400 rounded-md transition cursor-pointer"
                >
                  <div class="flex items-center gap-2">
                    <component :is="nav.icon" class="w-4 h-4" />
                    <span>{{ nav.name }}</span>
                  </div>
                  <kbd class="ml-auto text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded">⌘{{ nav.shortcut }}</kbd>
                </div>
              </div>

              <div v-if="filteredActions.length" class="px-4 pt-4 text-xs font-semibold text-green-400">
                <h1 class="ml-5 text-gray-400">Actions</h1>
                <div
                  v-for="action in filteredActions"
                  :key="action.name"
                  @click="navigateTo(action.route)"
                  class="flex items-center justify-between px-4 py-2 hover:bg-green-400/10 hover:text-green-400 rounded-md transition cursor-pointer"
                >
                  <PlusIcon class="w-4 h-4" />
                  <span>{{ action.name }}</span>
                  <kbd class="ml-auto text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded">⌘{{ action.shortcut }}</kbd>
                </div>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </div>

    <div class="flex items-center gap-3">
      <!-- Notifications and Settings omitted for brevity -->
       <div class="flex items-center gap-3">
      <DropdownMenu>
        <DropdownMenuTrigger as-child>
          <Button class="hover:bg-[#00C951] hover:text-white bg-white text-black border border-green-300">
            <Icon name="lucide:bell" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" class="w-72 bg-white text-black rounded-xl p-4 shadow-xl">
          <div class="font-bold text-lg mb-1">Notifications</div>
          <div class="text-sm text-gray-400 mb-4">You have 3 unread messages.</div>

          <div class="bg-white rounded-lg p-3 flex items-center justify-between mb-4 shadow">
            <div class="flex items-center gap-3">
              <Icon name="lucide:bell" class="text-green-500" />
              <div>
                <div class="font-semibold">Push Notifications</div>
                <div class="text-sm text-gray-400">Send notifications to device.</div>
              </div>
            </div>
            
<label class="inline-flex items-center cursor-pointer">
  <input type="checkbox" value="" class="sr-only peer" checked>
  <div class="relative w-11 h-6 bg-gray-200 rounded-full peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-400"></div>
  <span class="ms-3 text-sm font-medium text-gray-900"></span>
</label>

          </div>

          <div class="space-y-4">
            <div v-for="(note, index) in notifications" :key="index" class="flex gap-3 items-start">
              <span class="w-2 h-2 mt-2 bg-[#00C8AA] rounded-full"></span>
              <div>
                <div class="font-semibold">{{ note.title }}</div>
                <div class="text-sm text-gray-400">{{ note.time }}</div>
              </div>
            </div>
          </div>

          <div class="mt-5">
            <button class="w-full bg-[#00C951] hover:bg-[#00b04a] text-black font-semibold py-2 rounded-lg">
              ✓ Mark all as read
            </button>
          </div>
        </DropdownMenuContent>
      </DropdownMenu>

      <DropdownMenu>
        <DropdownMenuTrigger as-child>
          <Button class="hover:bg-[#00C951] hover:text-white bg-white text-black border border-green-300">
            <Icon name="lucide:settings" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" class="w-72">
          <DropdownMenuLabel>My Account</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <div class="w-full flex items-center pl-3 p-2 gap-2">
            <Avatar>
              <AvatarFallback class="bg-gray-400 text-white font-bold">
                {{ authStore.userName.charAt(0).toUpperCase() || 'U' }}
              </AvatarFallback>
            </Avatar>
            <div>
              <div class="font-bold text-gray-800">{{ authStore.userName || 'User' }}</div>
              <div class="font-bold text-gray-600 text-sm">{{ authStore.userEmail || 'No email' }}</div>
            </div>
          </div>
          <DropdownMenuSeparator />
          <DropdownMenuItem>Profile</DropdownMenuItem>
          <DropdownMenuItem>Billing</DropdownMenuItem>
          <DropdownMenuItem>Team</DropdownMenuItem>
          <DropdownMenuItem>Subscription</DropdownMenuItem>
          <DropdownMenuItem @click="handleLogout" class="cursor-pointer text-red-600 hover:text-red-700 hover:bg-red-50">
            <Icon class="mr-2 h-4 w-4" name="heroicons:arrow-left-on-rectangle" />
            <span class="ml-2">Logout</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
      <div>
        <UserProfile />
      </div>
    </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useToast } from 'vue-toastification'
import { useAuthStore } from '~/stores/auth'
import { PlusIcon, SettingsIcon, UserIcon, FileTextIcon, LayoutDashboardIcon, PackageIcon } from 'lucide-vue-next'

const router = useRouter()
const toast = useToast()
const authStore = useAuthStore()
const searchQuery = ref('')
const open = ref(false)

const navigation = [
  { name: 'Go to Dashboard', route: '/', icon: LayoutDashboardIcon, shortcut: 'D' },
  { name: 'Go to Clients', route: '/clients', icon: UserIcon, shortcut: 'C' },
  { name: 'Go to Invoices', route: '/invoices', icon: FileTextIcon, shortcut: 'I' },
  { name: 'Go to Products', route: '/products', icon: PackageIcon, shortcut: 'P' },
  { name: 'Go to Settings', route: '/settings', icon: SettingsIcon, shortcut: 'S' }
]

const actions = [
  { name: 'Create New Invoice', route: '/invoices/create', shortcut: 'N' },
  { name: 'Add New Client', route: '/clients/create', shortcut: 'C' },
  { name: 'Add Product/Service', route: '/products/create', shortcut: 'P' }
]
const notifications = [
  { title: 'Your call has been confirmed.', time: '1 hour ago' },
  { title: 'You have a new message!', time: '1 hour ago' },
  { title: 'Your subscription is expiring soon!', time: '2 hours ago' }
]

const filteredNavigation = computed(() =>
  navigation.filter(item =>
    item.name.toLowerCase().includes(searchQuery.value.toLowerCase())
  )
)

const filteredActions = computed(() =>
  actions.filter(item =>
    item.name.toLowerCase().includes(searchQuery.value.toLowerCase())
  )
)

function navigateTo(route: string) {
  open.value = false
  searchQuery.value = ''
  router.push(route)
}

const handleLogout = async () => {
  try {
    await authStore.logout()
    toast.success('Logged out successfully!')
  } catch (error: any) {
    console.error('Logout error:', error)
    toast.error(error.message || 'Failed to logout. Please try again.')
  }
}
</script>
