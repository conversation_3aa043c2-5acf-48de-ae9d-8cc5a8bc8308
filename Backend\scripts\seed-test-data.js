// Script to seed test data for development
const mongoose = require('mongoose');
require('dotenv').config();

// Import models
const Client = require('../models/Client');
const Invoice = require('../models/Invoice');

// Test user ID (this would normally come from Supabase)
const TEST_USER_ID = 'test-user-123';

const sampleClients = [
  {
    userId: TEST_USER_ID,
    clientId: 'CLI-TEST-001',
    name: '<PERSON>',
    email: '<EMAIL>',
    type: 'Individual',
    phone: '******-0123',
    address: {
      street: '123 Main St',
      city: 'New York',
      state: 'NY',
      zipCode: '10001',
      country: 'USA'
    },
    status: 'Active',
    totalInvoices: 15,
    totalAmount: 32100,
    outstandingAmount: 5000
  },
  {
    userId: TEST_USER_ID,
    clientId: 'CLI-TEST-002',
    name: 'Acme Corporation',
    email: '<EMAIL>',
    type: 'Business',
    phone: '******-0456',
    company: {
      name: 'Acme Corporation',
      website: 'https://acme.com',
      taxId: 'TAX123456'
    },
    address: {
      street: '456 Business Ave',
      city: 'Los Angeles',
      state: 'CA',
      zipCode: '90210',
      country: 'USA'
    },
    status: 'Active',
    totalInvoices: 12,
    totalAmount: 25400,
    outstandingAmount: 3200
  },
  {
    userId: TEST_USER_ID,
    clientId: 'CLI-TEST-003',
    name: 'Marketing Inc',
    email: '<EMAIL>',
    type: 'Business',
    phone: '******-0789',
    company: {
      name: 'Marketing Inc',
      website: 'https://marketinginc.com'
    },
    address: {
      street: '789 Creative Blvd',
      city: 'Chicago',
      state: 'IL',
      zipCode: '60601',
      country: 'USA'
    },
    status: 'Inactive',
    totalInvoices: 6,
    totalAmount: 18900,
    outstandingAmount: 0
  },
  {
    userId: TEST_USER_ID,
    clientId: 'CLI-TEST-004',
    name: 'Tech Solutions Ltd',
    email: '<EMAIL>',
    type: 'Business',
    phone: '******-0321',
    company: {
      name: 'Tech Solutions Ltd',
      website: 'https://techsolutions.com'
    },
    address: {
      street: '321 Tech Park',
      city: 'San Francisco',
      state: 'CA',
      zipCode: '94105',
      country: 'USA'
    },
    status: 'Active',
    totalInvoices: 8,
    totalAmount: 15600,
    outstandingAmount: 2400
  }
];

const sampleInvoices = [
  {
    userId: TEST_USER_ID,
    invoiceNumber: 'INV-**********',
    clientName: 'John Wilson',
    clientEmail: '<EMAIL>',
    items: [
      {
        description: 'Web Development Services',
        quantity: 40,
        unitPrice: 100,
        total: 4000
      }
    ],
    subtotal: 4000,
    taxRate: 10,
    taxAmount: 400,
    total: 4400,
    status: 'paid',
    issueDate: new Date('2024-01-15'),
    dueDate: new Date('2024-02-15')
  },
  {
    userId: TEST_USER_ID,
    invoiceNumber: 'INV-**********',
    clientName: 'Acme Corporation',
    clientEmail: '<EMAIL>',
    items: [
      {
        description: 'Consulting Services',
        quantity: 20,
        unitPrice: 150,
        total: 3000
      }
    ],
    subtotal: 3000,
    taxRate: 8.5,
    taxAmount: 255,
    total: 3255,
    status: 'sent',
    issueDate: new Date('2024-01-20'),
    dueDate: new Date('2024-02-20')
  },
  {
    userId: TEST_USER_ID,
    invoiceNumber: 'INV-**********',
    clientName: 'Tech Solutions Ltd',
    clientEmail: '<EMAIL>',
    items: [
      {
        description: 'Software Development',
        quantity: 60,
        unitPrice: 120,
        total: 7200
      }
    ],
    subtotal: 7200,
    taxRate: 10,
    taxAmount: 720,
    total: 7920,
    status: 'paid',
    issueDate: new Date('2024-01-25'),
    dueDate: new Date('2024-02-25')
  }
];

async function seedData() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');

    // Clear existing test data
    await Client.deleteMany({ userId: TEST_USER_ID });
    await Invoice.deleteMany({ userId: TEST_USER_ID });
    console.log('Cleared existing test data');

    // Insert sample clients
    const clients = await Client.insertMany(sampleClients);
    console.log(`Inserted ${clients.length} sample clients`);

    // Insert sample invoices
    const invoices = await Invoice.insertMany(sampleInvoices);
    console.log(`Inserted ${invoices.length} sample invoices`);

    console.log('✅ Test data seeded successfully!');
    console.log(`Test User ID: ${TEST_USER_ID}`);
    
  } catch (error) {
    console.error('❌ Error seeding data:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

// Run the seeding
seedData();
