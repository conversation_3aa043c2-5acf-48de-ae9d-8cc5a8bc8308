const Expense = require('../models/Expense');
const mongoose = require('mongoose');

/**
 * @desc    Get all expenses for authenticated user
 * @route   GET /api/expenses
 * @access  Private (Protected by Supabase JWT)
 */
const getExpenses = async (req, res) => {
  try {
    const userId = req.user.id;
    const {
      page = 1,
      limit = 10,
      search,
      status,
      category,
      sort = '-date',
      startDate,
      endDate
    } = req.query;

    const options = {
      status,
      category,
      search,
      sort,
      startDate,
      endDate,
      limit: parseInt(limit),
      skip: (parseInt(page) - 1) * parseInt(limit)
    };

    // Get expenses with pagination
    const expenses = await Expense.findByUser(userId, options);
    
    // Build count query
    let countQuery = { userId };
    if (status && status !== 'all') countQuery.status = status;
    if (category && category !== 'all') countQuery.category = category;
    if (search) {
      countQuery.$or = [
        { vendor: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { expenseId: { $regex: search, $options: 'i' } }
      ];
    }
    if (startDate || endDate) {
      countQuery.date = {};
      if (startDate) countQuery.date.$gte = new Date(startDate);
      if (endDate) countQuery.date.$lte = new Date(endDate);
    }

    const totalExpenses = await Expense.countDocuments(countQuery);
    const totalPages = Math.ceil(totalExpenses / parseInt(limit));

    res.status(200).json({
      success: true,
      count: expenses.length,
      totalExpenses,
      totalPages,
      currentPage: parseInt(page),
      data: expenses,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages,
        totalExpenses,
        hasNextPage: parseInt(page) < totalPages,
        hasPrevPage: parseInt(page) > 1
      }
    });
  } catch (error) {
    console.error('Get expenses error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve expenses',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Get single expense by ID
 * @route   GET /api/expenses/:id
 * @access  Private (Protected by Supabase JWT)
 */
const getExpense = async (req, res) => {
  try {
    const userId = req.user.id;
    const expenseId = req.params.id;

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(expenseId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid expense ID format'
      });
    }

    const expense = await Expense.findOne({ 
      _id: expenseId, 
      userId 
    });

    if (!expense) {
      return res.status(404).json({
        success: false,
        message: 'Expense not found'
      });
    }

    res.status(200).json({
      success: true,
      data: expense
    });
  } catch (error) {
    console.error('Get expense error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve expense',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Create new expense
 * @route   POST /api/expenses
 * @access  Private (Protected by Supabase JWT)
 */
const createExpense = async (req, res) => {
  try {
    const userId = req.user.id;
    
    // Add userId to the expense data
    const expenseData = {
      ...req.body,
      userId
    };

    const expense = new Expense(expenseData);
    const savedExpense = await expense.save();

    res.status(201).json({
      success: true,
      message: 'Expense created successfully',
      data: savedExpense
    });
  } catch (error) {
    console.error('Create expense error:', error);
    
    // Handle validation errors
    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map(err => err.message);
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: messages
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to create expense',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Update expense
 * @route   PUT /api/expenses/:id
 * @access  Private (Protected by Supabase JWT)
 */
const updateExpense = async (req, res) => {
  try {
    const userId = req.user.id;
    const expenseId = req.params.id;

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(expenseId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid expense ID format'
      });
    }

    // Check if expense exists and belongs to user
    const existingExpense = await Expense.findOne({ 
      _id: expenseId, 
      userId 
    });

    if (!existingExpense) {
      return res.status(404).json({
        success: false,
        message: 'Expense not found'
      });
    }

    // Update expense
    const updatedExpense = await Expense.findByIdAndUpdate(
      expenseId,
      { ...req.body, updatedAt: new Date() },
      { 
        new: true, 
        runValidators: true 
      }
    );

    res.status(200).json({
      success: true,
      message: 'Expense updated successfully',
      data: updatedExpense
    });
  } catch (error) {
    console.error('Update expense error:', error);
    
    // Handle validation errors
    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map(err => err.message);
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: messages
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to update expense',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Delete expense
 * @route   DELETE /api/expenses/:id
 * @access  Private (Protected by Supabase JWT)
 */
const deleteExpense = async (req, res) => {
  try {
    const userId = req.user.id;
    const expenseId = req.params.id;

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(expenseId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid expense ID format'
      });
    }

    const expense = await Expense.findOneAndDelete({ 
      _id: expenseId, 
      userId 
    });

    if (!expense) {
      return res.status(404).json({
        success: false,
        message: 'Expense not found'
      });
    }

    res.status(200).json({
      success: true,
      message: 'Expense deleted successfully',
      data: expense
    });
  } catch (error) {
    console.error('Delete expense error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete expense',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Get expense statistics
 * @route   GET /api/expenses/stats
 * @access  Private (Protected by Supabase JWT)
 */
const getExpenseStats = async (req, res) => {
  try {
    const userId = req.user.id;
    const { startDate, endDate } = req.query;

    const stats = await Expense.getStats(userId, { startDate, endDate });

    const result = stats[0] || {
      totalExpenses: 0,
      totalCount: 0,
      pendingAmount: 0,
      approvedAmount: 0,
      paidAmount: 0,
      rejectedAmount: 0,
      pendingCount: 0,
      approvedCount: 0,
      paidCount: 0,
      rejectedCount: 0
    };

    res.status(200).json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('Get expense stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve expense statistics',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Get expense categories
 * @route   GET /api/expenses/categories
 * @access  Private (Protected by Supabase JWT)
 */
const getExpenseCategories = async (_req, res) => {
  try {
    const categories = [
      'Office Supplies',
      'Equipment',
      'Travel',
      'Meals & Entertainment',
      'Marketing',
      'Software & Subscriptions',
      'Utilities',
      'Rent',
      'Insurance',
      'Professional Services',
      'Training & Education',
      'Maintenance & Repairs',
      'Other'
    ];

    res.status(200).json({
      success: true,
      data: categories
    });
  } catch (error) {
    console.error('Get expense categories error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve expense categories',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Approve expense
 * @route   PUT /api/expenses/:id/approve
 * @access  Private (Protected by Supabase JWT)
 */
const approveExpense = async (req, res) => {
  try {
    const userId = req.user.id;
    const expenseId = req.params.id;

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(expenseId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid expense ID format'
      });
    }

    const expense = await Expense.findOneAndUpdate(
      { _id: expenseId, userId },
      {
        status: 'approved',
        approvedBy: userId,
        approvedAt: new Date(),
        rejectionReason: undefined
      },
      { new: true, runValidators: true }
    );

    if (!expense) {
      return res.status(404).json({
        success: false,
        message: 'Expense not found'
      });
    }

    res.status(200).json({
      success: true,
      message: 'Expense approved successfully',
      data: expense
    });
  } catch (error) {
    console.error('Approve expense error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to approve expense',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Reject expense
 * @route   PUT /api/expenses/:id/reject
 * @access  Private (Protected by Supabase JWT)
 */
const rejectExpense = async (req, res) => {
  try {
    const userId = req.user.id;
    const expenseId = req.params.id;
    const { reason } = req.body;

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(expenseId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid expense ID format'
      });
    }

    const expense = await Expense.findOneAndUpdate(
      { _id: expenseId, userId },
      {
        status: 'rejected',
        rejectionReason: reason,
        approvedBy: undefined,
        approvedAt: undefined
      },
      { new: true, runValidators: true }
    );

    if (!expense) {
      return res.status(404).json({
        success: false,
        message: 'Expense not found'
      });
    }

    res.status(200).json({
      success: true,
      message: 'Expense rejected successfully',
      data: expense
    });
  } catch (error) {
    console.error('Reject expense error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to reject expense',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

module.exports = {
  getExpenses,
  getExpense,
  createExpense,
  updateExpense,
  deleteExpense,
  getExpenseStats,
  getExpenseCategories,
  approveExpense,
  rejectExpense
};
