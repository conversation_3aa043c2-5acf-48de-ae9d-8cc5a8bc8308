<template>
    <div>
      <!-- Heading -->
      <Heading
    title="Hello, Sundhar"
    description="Talxwev sample user business name"
    imageUrl="/imageUrl/invoice-easy-logo.png"
    iconColor="text-indigo-600"
    bgColor="bg-white"
    :buttons="[
      {
        label: 'Create Invoice',
        icon: 'lucide:circle-plus',
        bgColor: 'bg-[#00C951]',
        textColor: 'text-white',
        to: '/invoices'
      },
    ]"
      />

      <!-- cards -->
      <div class="mb-8">
        <Cards/>
      </div>

      <!-- Recent Invoice Quick Action -->
      <div class="mb-8">
        <RecentInvoiceQuickAction/>
      </div>
    </div>
  </template>
  
  <script lang="ts" setup>
  definePageMeta({
  middleware: 'auth'
})

  </script>
  
  <style>
  /* Add any custom styles if needed */
  </style>
  