import { defineStore } from 'pinia'
import { productApi, type Product, type ProductStats, type ProductParams, type StockUpdateData } from '~/services/productApi'

interface ProductsState {
  products: Product[]
  currentProduct: Product | null
  stats: ProductStats | null
  categories: string[]
  loading: boolean
  error: string | null
  pagination: {
    page: number
    limit: number
    totalPages: number
    totalProducts: number
    hasNextPage: boolean
    hasPrevPage: boolean
  } | null
}

export const useProductsStore = defineStore('products', {
  state: (): ProductsState => ({
    products: [],
    currentProduct: null,
    stats: null,
    categories: [],
    loading: false,
    error: null,
    pagination: null
  }),

  getters: {
    // Get products by status
    getProductsByStatus: (state) => (status: string) => {
      if (status === 'all') return state.products
      return state.products.filter(product => product.status === status)
    },

    // Get products by category
    getProductsByCategory: (state) => (category: string) => {
      if (category === 'all') return state.products
      return state.products.filter(product => product.category === category)
    },

    // Get product by ID
    getProductById: (state) => (id: string) => {
      return state.products.find(product => product._id === id)
    },

    // Get low stock products
    lowStockProducts: (state) => {
      return state.products.filter(product => product.stock <= product.minStockLevel)
    },

    // Get active products
    activeProducts: (state) => {
      return state.products.filter(product => product.status === 'active')
    },

    // Calculate total inventory value
    totalInventoryValue: (state) => {
      return state.products.reduce((total, product) => total + (product.stock * product.costPrice), 0)
    },

    // Calculate total potential revenue
    totalPotentialRevenue: (state) => {
      return state.products.reduce((total, product) => total + (product.stock * product.sellingPrice), 0)
    },

    // Get products count by status
    productsCountByStatus: (state) => {
      const counts = {
        active: 0,
        inactive: 0,
        discontinued: 0
      }
      
      state.products.forEach(product => {
        if (counts.hasOwnProperty(product.status)) {
          counts[product.status as keyof typeof counts]++
        }
      })
      
      return counts
    },

    // Check if there are any products
    hasProducts: (state) => state.products.length > 0,

    // Check if loading
    isLoading: (state) => state.loading,

    // Check if there's an error
    hasError: (state) => !!state.error
  },

  actions: {
    // Set loading state
    setLoading(loading: boolean) {
      this.loading = loading
    },

    // Set error state
    setError(error: string | null) {
      this.error = error
    },

    // Clear error
    clearError() {
      this.error = null
    },

    // Fetch all products
    async fetchProducts(params?: ProductParams) {
      try {
        this.setLoading(true)
        this.clearError()

        const response = await productApi.getProducts(params)
        
        this.products = response.data
        this.pagination = response.pagination

        console.log('✅ Products fetched successfully:', response.data.length)
      } catch (error) {
        console.error('❌ Error fetching products:', error)
        this.setError(error instanceof Error ? error.message : 'Failed to fetch products')
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // Fetch single product
    async fetchProduct(id: string) {
      try {
        this.setLoading(true)
        this.clearError()

        const response = await productApi.getProduct(id)
        
        this.currentProduct = response.data

        // Update the product in the list if it exists
        const index = this.products.findIndex(product => product._id === id)
        if (index !== -1) {
          this.products[index] = response.data
        }

        console.log('✅ Product fetched successfully:', response.data.name)
        return response.data
      } catch (error) {
        console.error('❌ Error fetching product:', error)
        this.setError(error instanceof Error ? error.message : 'Failed to fetch product')
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // Create new product
    async createProduct(productData: Omit<Product, '_id' | 'userId' | 'productId' | 'totalSold' | 'totalRevenue' | 'createdAt' | 'updatedAt'>) {
      try {
        this.setLoading(true)
        this.clearError()

        const response = await productApi.createProduct(productData)
        
        // Add the new product to the beginning of the list
        this.products.unshift(response.data)
        this.currentProduct = response.data

        console.log('✅ Product created successfully:', response.data.name)
        return response.data
      } catch (error) {
        console.error('❌ Error creating product:', error)
        this.setError(error instanceof Error ? error.message : 'Failed to create product')
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // Update product
    async updateProduct(id: string, productData: Partial<Product>) {
      try {
        this.setLoading(true)
        this.clearError()

        const response = await productApi.updateProduct(id, productData)
        
        // Update the product in the list
        const index = this.products.findIndex(product => product._id === id)
        if (index !== -1) {
          this.products[index] = response.data
        }

        // Update current product if it's the same
        if (this.currentProduct?._id === id) {
          this.currentProduct = response.data
        }

        console.log('✅ Product updated successfully:', response.data.name)
        return response.data
      } catch (error) {
        console.error('❌ Error updating product:', error)
        this.setError(error instanceof Error ? error.message : 'Failed to update product')
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // Delete product
    async deleteProduct(id: string) {
      try {
        this.setLoading(true)
        this.clearError()

        await productApi.deleteProduct(id)
        
        // Remove the product from the list
        this.products = this.products.filter(product => product._id !== id)

        // Clear current product if it's the deleted one
        if (this.currentProduct?._id === id) {
          this.currentProduct = null
        }

        console.log('✅ Product deleted successfully')
      } catch (error) {
        console.error('❌ Error deleting product:', error)
        this.setError(error instanceof Error ? error.message : 'Failed to delete product')
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // Fetch product statistics
    async fetchProductStats() {
      try {
        this.clearError()

        const response = await productApi.getProductStats()
        
        this.stats = response.data

        console.log('✅ Product stats fetched successfully')
        return response.data
      } catch (error) {
        console.error('❌ Error fetching product stats:', error)
        this.setError(error instanceof Error ? error.message : 'Failed to fetch product statistics')
        throw error
      }
    },

    // Fetch product categories
    async fetchProductCategories() {
      try {
        this.clearError()

        const response = await productApi.getProductCategories()
        
        this.categories = response.data

        console.log('✅ Product categories fetched successfully')
        return response.data
      } catch (error) {
        console.error('❌ Error fetching product categories:', error)
        this.setError(error instanceof Error ? error.message : 'Failed to fetch product categories')
        throw error
      }
    },

    // Update product stock
    async updateProductStock(id: string, stockData: StockUpdateData) {
      try {
        this.setLoading(true)
        this.clearError()

        const response = await productApi.updateProductStock(id, stockData)
        
        // Update the product in the list
        const index = this.products.findIndex(product => product._id === id)
        if (index !== -1) {
          this.products[index] = response.data
        }

        // Update current product if it's the same
        if (this.currentProduct?._id === id) {
          this.currentProduct = response.data
        }

        console.log('✅ Product stock updated successfully')
        return response.data
      } catch (error) {
        console.error('❌ Error updating product stock:', error)
        this.setError(error instanceof Error ? error.message : 'Failed to update product stock')
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // Set current product
    setCurrentProduct(product: Product | null) {
      this.currentProduct = product
    },

    // Clear all data
    clearData() {
      this.products = []
      this.currentProduct = null
      this.stats = null
      this.categories = []
      this.pagination = null
      this.error = null
    },

    // Refresh products (re-fetch with current params)
    async refreshProducts() {
      await this.fetchProducts()
    }
  }
})
