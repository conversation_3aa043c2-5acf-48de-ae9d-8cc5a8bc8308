<template>
  <div class="p-4 space-y-6">
    <!-- Loading State -->
    <div v-if="expensesStore.isLoading" class="flex justify-center items-center py-8">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-[#00C951]"></div>
      <span class="ml-2 text-gray-600">Loading expense statistics...</span>
    </div>

    <!-- Error State -->
    <div v-else-if="expensesStore.hasError" class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
      <div class="flex items-center">
        <Icon name="lucide:alert-circle" class="w-5 h-5 text-red-500 mr-2"/>
        <span class="text-red-700">{{ expensesStore.error }}</span>
      </div>
      <button
        @click="refreshData"
        class="mt-2 text-sm text-red-600 hover:text-red-800 underline"
      >
        Try again
      </button>
    </div>

    <!-- Top Summary Cards -->
    <div v-else class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
      <SummaryCard
        :icon="BadgeDollarSign"
        title="Total Expenses"
        :value="formatCurrency(expensesStore.stats?.totalExpenses || 0)"
        iconColor="text-indigo-500"
      />
      <SummaryCard
        :icon="Clock"
        title="Pending"
        :value="`${expensesStore.stats?.pendingCount || 0} (${formatCurrency(expensesStore.stats?.pendingAmount || 0)})`"
        iconColor="text-yellow-500"
      />
      <SummaryCard
        :icon="CheckCircle"
        title="Paid"
        :value="`${expensesStore.stats?.paidCount || 0} (${formatCurrency(expensesStore.stats?.paidAmount || 0)})`"
        iconColor="text-green-500"
      />
      <SummaryCard
        :icon="CheckCheck"
        title="Approved"
        :value="`${expensesStore.stats?.approvedCount || 0} (${formatCurrency(expensesStore.stats?.approvedAmount || 0)})`"
        iconColor="text-blue-500"
      />
      <SummaryCard
        :icon="AlertCircle"
        title="Rejected"
        :value="`${expensesStore.stats?.rejectedCount || 0} (${formatCurrency(expensesStore.stats?.rejectedAmount || 0)})`"
        iconColor="text-red-500"
      />
    </div>

    <!-- Table Container -->
<div class="p-6 bg-white rounded-xl shadow-lg border space-y-6">

  <!-- Search & Filters -->
  <div class="flex flex-col md:flex-row md:items-center justify-between gap-4">
    <!-- search icon -->
        <div class="relative transition-all duration-300 ease-in-out">
          <div
            class="flex items-center border rounded-lg px-3 py-2 w-28 md:w-80 hover:w-56 hover:md:w-96 transition-all duration-300 ease-in-out bg-white shadow-sm focus-within:ring-2"
            style="--tw-ring-color: #05DF72"
          >
            <Icon name="lucide:search" class="w-5 h-5 text-gray-400" />
            <input
              v-model="search"
              type="text"
              placeholder="Search..."
              class="ml-2 outline-none w-full bg-transparent text-sm text-gray-600"
            />
          </div>
        </div>
    <div class="flex gap-2">
      <select
        v-model="status"
        class="border border-gray-300 rounded-lg px-4 py-2 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200"
      >
        <option>All Status</option>
        <option>paid</option>
        <option>pending</option>
      </select>
      <select
        v-model="category"
        class="border border-gray-300 rounded-lg px-4 py-2 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200"
      >
        <option>All Categories</option>
        <option>Office Supplies</option>
        <option>Equipment</option>
      </select>
    </div>
  </div>

  <!-- Table -->
  <div class="overflow-x-auto">
    <table class="min-w-full bg-white rounded-md shadow">
      <thead class="bg-gray-50 text-sm text-gray-700 text-left">
        <tr>
          <th class="p-3 font-medium">Expense ID</th>
          <th class="p-3 font-medium">Vendor</th>
          <th class="p-3 font-medium">Category</th>
          <th class="p-3 font-medium">Date</th>
          <th class="p-3 font-medium">Amount</th>
          <th class="p-3 font-medium">Status</th>
          <th class="p-3 font-medium">Actions</th>
        </tr>
      </thead>
      <tbody>
        <!-- Loading State -->
        <tr v-if="expensesStore.isLoading">
          <td colspan="7" class="p-8 text-center">
            <div class="flex justify-center items-center">
              <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-[#00C951]"></div>
              <span class="ml-2 text-gray-600">Loading expenses...</span>
            </div>
          </td>
        </tr>

        <!-- Error State -->
        <tr v-else-if="expensesStore.hasError">
          <td colspan="7" class="p-8 text-center">
            <div class="text-red-600">{{ expensesStore.error }}</div>
            <button
              @click="refreshData"
              class="mt-2 text-sm text-red-600 hover:text-red-800 underline"
            >
              Try again
            </button>
          </td>
        </tr>

        <!-- No Data State -->
        <tr v-else-if="filteredExpenses.length === 0">
          <td colspan="7" class="p-8 text-center text-gray-500">
            No expenses found
          </td>
        </tr>

        <!-- Expense Rows -->
        <tr
          v-else
          v-for="expense in filteredExpenses"
          :key="expense._id"
          class="border-t hover:bg-gray-50 transition-colors duration-150 text-sm"
        >
          <td class="p-3 font-semibold">{{ expense.expenseId }}</td>
          <td class="p-3 font-semibold">{{ expense.vendor }}</td>
          <td class="p-3">{{ expense.category }}</td>
          <td class="p-3">{{ formatDate(expense.date) }}</td>
          <td class="p-3">${{ expense.totalAmount.toLocaleString() }}</td>
          <td class="p-3">
            <span :class="getStatusClasses(expense.status)">
              <component :is="getStatusIcon(expense.status)" class="w-4 h-4" />
              {{ expense.status }}
            </span>
          </td>
          <td class="p-3 flex items-center gap-4 text-gray-600">
            <Eye
              class="w-4 h-4 cursor-pointer hover:text-blue-500 transition-colors"
              @click="viewExpense(expense)"
            />
            <Pencil
              class="w-4 h-4 cursor-pointer hover:text-indigo-500 transition-colors"
              @click="editExpense(expense)"
            />
            <Download
              class="w-4 h-4 cursor-pointer hover:text-green-500 transition-colors"
              @click="downloadReceipt(expense)"
            />
            <Trash
              class="w-4 h-4 text-red-500 cursor-pointer hover:text-red-600 transition-colors"
              @click="deleteExpense(expense)"
            />
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>


  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useExpensesStore } from '~/stores/expenses'

// Lucide icons
import {
  BadgeDollarSign,
  Clock,
  CheckCircle,
  CheckCheck,
  AlertCircle,
  Eye,
  Pencil,
  Download,
  Trash
} from 'lucide-vue-next'

const expensesStore = useExpensesStore()

const search = ref('')
const status = ref('All Status')
const category = ref('All Categories')

// Fetch data on component mount
onMounted(async () => {
  try {
    await Promise.all([
      expensesStore.fetchExpenses({ limit: 20 }),
      expensesStore.fetchExpenseStats(),
      expensesStore.fetchExpenseCategories()
    ])
  } catch (error) {
    console.error('Failed to load expense data:', error)
  }
})

// Refresh all data
const refreshData = async () => {
  try {
    await Promise.all([
      expensesStore.fetchExpenses({ limit: 20 }),
      expensesStore.fetchExpenseStats(),
      expensesStore.fetchExpenseCategories()
    ])
  } catch (error) {
    console.error('Failed to refresh expense data:', error)
  }
}

// Helper function to format currency
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount)
}

// Helper function to format date
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

// Helper function to get status classes
const getStatusClasses = (status: string) => {
  const baseClasses = 'px-3 py-1 rounded-full text-sm font-medium flex items-center gap-1 w-max'

  switch (status.toLowerCase()) {
    case 'paid':
      return `${baseClasses} bg-green-100 text-green-700`
    case 'approved':
      return `${baseClasses} bg-blue-100 text-blue-700`
    case 'pending':
      return `${baseClasses} bg-yellow-100 text-yellow-700`
    case 'rejected':
      return `${baseClasses} bg-red-100 text-red-700`
    default:
      return `${baseClasses} bg-gray-100 text-gray-700`
  }
}

// Helper function to get status icon
const getStatusIcon = (status: string) => {
  switch (status.toLowerCase()) {
    case 'paid':
      return CheckCircle
    case 'approved':
      return CheckCheck
    case 'pending':
      return Clock
    case 'rejected':
      return AlertCircle
    default:
      return Clock
  }
}

// Action handlers
const viewExpense = (expense: any) => {
  console.log('View expense:', expense)
  // TODO: Navigate to expense detail page
}

const editExpense = (expense: any) => {
  console.log('Edit expense:', expense)
  // TODO: Navigate to expense edit page
}

const downloadReceipt = (expense: any) => {
  console.log('Download receipt:', expense)
  // TODO: Download receipt functionality
}

const deleteExpense = async (expense: any) => {
  if (confirm(`Are you sure you want to delete expense ${expense.expenseId}?`)) {
    try {
      await expensesStore.deleteExpense(expense._id)
      console.log('Expense deleted successfully')
    } catch (error) {
      console.error('Failed to delete expense:', error)
    }
  }
}

const filteredExpenses = computed(() => {
  return expensesStore.expenses.filter((e: any) => {
    const matchesSearch = e.vendor.toLowerCase().includes(search.value.toLowerCase()) ||
                         e.expenseId.toLowerCase().includes(search.value.toLowerCase())
    const matchesStatus = status.value === 'All Status' || e.status === status.value
    const matchesCategory = category.value === 'All Categories' || e.category === category.value
    return matchesSearch && matchesStatus && matchesCategory
  })
})
</script>
