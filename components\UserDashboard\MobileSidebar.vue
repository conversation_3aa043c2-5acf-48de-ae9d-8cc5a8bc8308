<template>
  <div>
    <Sheet v-model:open="isMobileSidebarOpen">
      <SheetTrigger as-child>
        <Button variant="outline" class="md:hidden pr-4 hover:opacity-75 transition">
          <Icon name="lucide:square-menu" />
        </Button>
      </SheetTrigger>
      <SheetContent side="left" class="p-0 bg-white">
        <Sidebar @navigate="isMobileSidebarOpen = false" />
      </SheetContent>
    </Sheet>
  </div>
</template>

<script setup lang="ts">
const isMobileSidebarOpen = ref(false)

function handleResize() {
  if (window.innerWidth >= 768) {
    isMobileSidebarOpen.value = false
  }
}

onMounted(() => {
  window.addEventListener("resize", handleResize)
})

onBeforeUnmount(() => {
  window.removeEventListener("resize", handleResize)
})
</script>
