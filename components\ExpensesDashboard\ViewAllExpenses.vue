<template>
  <div class="min-h-screen bg-white p-3">
    <div class="bg-white shadow-2xl rounded-xl p-6 space-y-6">
      <!-- Filters -->
      <div class="flex flex-col sm:flex-row gap-4">
        <!-- search -->
        <div class="relative transition-all duration-300 ease-in-out">
          <div
            class="flex items-center border rounded-lg px-3 py-2 w-28 md:w-80 hover:w-56 hover:md:w-96 transition-all duration-300 ease-in-out bg-white shadow-sm focus-within:ring-2"
            style="--tw-ring-color: #05DF72"
          >
            <Icon name="lucide:search" class="w-5 h-5 text-gray-400" />
            <input
              v-model="search"
              type="text"
              placeholder="Search..."
              class="ml-2 outline-none w-full bg-transparent text-sm text-gray-600"
            />
          </div>
        </div>
        <select v-model="selectedStatus" class="border rounded-lg px-4 py-2">
          <option value="">All Status</option>
          <option value="paid">Paid</option>
          <option value="pending">Pending</option>
          <option value="approved">Approved</option>
          <option value="rejected">Rejected</option>
        </select>
        <select v-model="selectedCategory" class="border rounded-lg px-4 py-2">
          <option value="">All Categories</option>
          <option>Office Supplies</option>
          <option>Travel</option>
          <option>Equipment</option>
          <option>Utilities</option>
          <option>Marketing</option>
        </select>
      </div>

      <!-- Expense Cards -->
      <div class="grid sm:grid-cols-2 lg:grid-cols-3 gap-6">
        <ExpenseCard
          v-for="expense in filteredExpenses"
          :key="expense.id"
          v-bind="expense"
        />
      </div>
    </div>
  </div>
</template>

<script setup>

import { ref, computed } from 'vue'

const search = ref('')
const selectedStatus = ref('')
const selectedCategory = ref('')

const expenses = ref([
  { id: 'EXP-001', vendor: 'Office Supplies Co', category: 'Office Supplies', amount: 5000, date: '2024-05-29', note: 'Printer cartridges and paper', status: 'paid' },
  { id: 'EXP-002', vendor: 'Tech Hardware', category: 'Equipment', amount: 25000, date: '2024-05-28', note: 'New laptop for development', status: 'pending' },
  { id: 'EXP-003', vendor: 'Travel Agency', category: 'Travel', amount: 12000, date: '2024-05-27', note: 'Business trip to Mumbai', status: 'approved' },
  { id: 'EXP-004', vendor: 'Internet Provider', category: 'Utilities', amount: 3500, date: '2024-05-26', note: 'Monthly internet bill', status: 'paid' },
  { id: 'EXP-005', vendor: 'Marketing Agency', category: 'Marketing', amount: 18000, date: '2024-05-25', note: 'Social media campaign', status: 'rejected' }
])

const filteredExpenses = computed(() => {
  return expenses.value.filter(exp => {
    return (
      (selectedStatus.value === '' || exp.status === selectedStatus.value) &&
      (selectedCategory.value === '' || exp.category === selectedCategory.value) &&
      (search.value === '' || exp.vendor.toLowerCase().includes(search.value.toLowerCase()))
    )
  })
})
</script>
