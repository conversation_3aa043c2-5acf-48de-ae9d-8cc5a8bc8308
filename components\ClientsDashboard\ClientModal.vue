<template>
  <Dialog :open="open" @update:open="onDialogUpdate">
    <DialogContent
      class="sm:max-w-3xl max-w-full max-h-[80vh] overflow-y-auto p-6 sm:p-8 scrollbar-hide"
    >
      <DialogHeader>
        <DialogTitle class="text-2xl font-semibold flex items-center gap-2">
          <UserIcon class="w-5 h-5" />
          Add New Client
        </DialogTitle>
      </DialogHeader>

      <div class="space-y-6">
        <!-- Toggle Buttons -->
        <div class="flex gap-4 flex-wrap">
          <Button
            :class="form.type === 'individual'
              ? 'bg-green-400 text-white'
              : 'border border-green-400 text-green-400'"
            @click="setType('individual')"
          >
            <UserIcon class="w-4 h-4 mr-2" /> Individual
          </Button>
          <Button
            :class="form.type === 'business'
              ? 'bg-green-400 text-white'
              : 'border border-green-400 text-green-400'"
            @click="setType('business')"
          >
            <Building2 class="w-4 h-4 mr-2" /> Business
          </Button>
        </div>

        <!-- Client ID Info -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
          <div class="flex items-center gap-2">
            <Icon name="lucide:info" class="w-4 h-4 text-blue-600" />
            <span class="text-sm font-medium text-blue-800">Client ID</span>
          </div>
          <p class="text-sm text-blue-700 mt-1">
            A unique Client ID will be automatically generated based on the client name (e.g., JOHN-DOE-001)
          </p>
        </div>

        <!-- Individual Form -->
        <div v-if="form.type === 'individual'" class="space-y-4">
          <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium mb-1">First Name *</label>
              <Input v-model="form.firstName" required />
            </div>
            <div>
              <label class="block text-sm font-medium mb-1">Last Name *</label>
              <Input v-model="form.lastName" required />
            </div>
            <div>
              <label class="block text-sm font-medium mb-1">Email *</label>
              <Input v-model="form.email" type="email" required />
            </div>
            <div>
              <label class="block text-sm font-medium mb-1">Phone *</label>
              <Input v-model="form.phone" type="tel" required />
            </div>
          </div>

          <div>
            <label class="block text-sm font-medium mb-1">Address</label>
            <Input v-model="form.address.street" placeholder="Street address" />
          </div>

          <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
            <div>
              <label class="block text-sm font-medium mb-1">City</label>
              <Input v-model="form.address.city" />
            </div>
            <div>
              <label class="block text-sm font-medium mb-1">State</label>
              <Input v-model="form.address.state" />
            </div>
            <div>
              <label class="block text-sm font-medium mb-1">Zip Code</label>
              <Input v-model="form.address.zipCode" />
            </div>
          </div>
           <div class="bg-white p-6 rounded-xl shadow-md border border-gray-200 max-w-4xl mx-auto mt-10">
    <h2 class="text-lg font-semibold text-gray-900 mb-6">Financial Settings</h2>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <!-- Currency Dropdown -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">Currency</label>
        <select
          v-model="currency"
          class="block w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 text-gray-700"
        >
          <option value="INR">INR (₹)</option>
          <option value="USD">USD ($)</option>
          <option value="EUR">EUR (€)</option>
        </select>
      </div>

      <!-- Payment Terms Dropdown -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">Payment Terms</label>
        <select
          v-model="paymentTerms"
          class="block w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 text-gray-700"
        >
          <option>Net 15</option>
          <option>Net 30</option>
          <option>Net 45</option>
          <option>Net 60</option>
          <option>Due on Receipt</option>
        </select>
      </div>
    </div>

    <!-- Notes -->
    <div class="mt-6">
      <label class="block text-sm font-medium text-gray-700 mb-2">Notes</label>
      <textarea
        v-model="notes"
        rows="4"
        class="block w-full px-4 py-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 text-gray-700"
        placeholder="Additional notes about this client..."
      ></textarea>
    </div>
  </div>
        </div>

        <!-- Business Form -->
        <div v-else class="space-y-4">
          <div>
            <label class="block text-sm font-medium mb-1">Company Name *</label>
            <Input v-model="form.companyName" required />
          </div>

          <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium mb-1">Contact Person *</label>
              <Input v-model="form.contactPerson" required />
            </div>
            <div>
              <label class="block text-sm font-medium mb-1">GST Number (Optional)</label>
              <Input v-model="form.gstNumber" />
            </div>
          </div>

          <div>
            <label class="block text-sm font-medium mb-1">Website</label>
            <Input v-model="form.website" type="url" placeholder="https://example.com" />
          </div>

          <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium mb-1">Email *</label>
              <Input v-model="form.email" type="email" required />
            </div>
            <div>
              <label class="block text-sm font-medium mb-1">Phone *</label>
              <Input v-model="form.phone" type="tel" required />
            </div>
          </div>

          <div>
            <label class="block text-sm font-medium mb-1">Address</label>
            <Input v-model="form.address.street" placeholder="Street address" />
          </div>

          <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
            <div>
              <label class="block text-sm font-medium mb-1">City</label>
              <Input v-model="form.address.city" />
            </div>
            <div>
              <label class="block text-sm font-medium mb-1">State</label>
              <Input v-model="form.address.state" />
            </div>
            <div>
              <label class="block text-sm font-medium mb-1">Zip Code</label>
              <Input v-model="form.address.zipCode" />
            </div>
          </div>
          <!-- financial terms -->
             <div class="bg-white p-6 rounded-xl shadow-md border border-gray-200 max-w-4xl mx-auto mt-10">
    <h2 class="text-lg font-semibold text-gray-900 mb-6">Financial Settings</h2>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <!-- Currency Dropdown -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">Currency</label>
        <select
          v-model="currency"
          class="block w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 text-gray-700"
        >
          <option value="INR">INR (₹)</option>
          <option value="USD">USD ($)</option>
          <option value="EUR">EUR (€)</option>
        </select>
      </div>

      <!-- Payment Terms Dropdown -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">Payment Terms</label>
        <select
          v-model="paymentTerms"
          class="block w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 text-gray-700"
        >
          <option>Net 15</option>
          <option>Net 30</option>
          <option>Net 45</option>
          <option>Net 60</option>
          <option>Due on Receipt</option>
        </select>
      </div>
    </div>

    <!-- Notes -->
    <div class="mt-6">
      <label class="block text-sm font-medium text-gray-700 mb-2">Notes</label>
      <textarea
        v-model="notes"
        rows="4"
        class="block w-full px-4 py-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 text-gray-700"
        placeholder="Additional notes about this client..."
      ></textarea>
    </div>
  </div>
        </div>

        <!-- Footer -->
        <DialogFooter class="flex justify-end gap-2 pt-4 flex-wrap">
          <Button variant="outline" @click="$emit('close')" :disabled="isSubmitting">Cancel</Button>
          <Button class="mt-2 sm:mt-0" @click="submit" :disabled="isSubmitting">
            <SaveIcon class="w-4 h-4 mr-2" />
            {{ isSubmitting ? 'Saving...' : (props.client?._id ? 'Update Client' : 'Save Client') }}
          </Button>
        </DialogFooter>
      </div>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, watch, nextTick } from 'vue'
import { UserIcon, Building2, SaveIcon } from 'lucide-vue-next'
import { useClientStore } from '~/stores/clients'
import { useAuthStore } from '~/stores/auth'
import { useToast } from 'vue-toastification'
import { useSearchManagement } from '~/composables/useSearchClear'

const props = defineProps({
  open: Boolean,
  client: Object as () => any // For editing existing clients
})
const emit = defineEmits(['close', 'client-saved'])

const clientStore = useClientStore()
const authStore = useAuthStore()
const toast = useToast()
const { clearSearchInputs } = useSearchManagement()
const isSubmitting = ref(false)
const currency = ref('INR')
const paymentTerms = ref('Net 30')
const notes = ref('')

const form = ref({
  type: 'individual',
  email: '',
  phone: '',
  firstName: '',
  lastName: '',
  companyName: '',
  contactPerson: '',
  gstNumber: '',
  website: '',
  address: {
    street: '',
    city: '',
    state: '',
    zipCode: '',
    country: 'India'
  },
  currency: 'INR',
  paymentTerms: 'Net 30',
  status: 'Active',
  notes: ''
})

function setType(type: 'individual' | 'business') {
  form.value.type = type
}



function resetForm() {
  form.value = {
    type: 'individual',
    email: '',
    phone: '',
    firstName: '',
    lastName: '',
    companyName: '',
    contactPerson: '',
    gstNumber: '',
    website: '',
    address: {
      street: '',
      city: '',
      state: '',
      zipCode: '',
      country: 'India'
    },
    currency: 'INR',
    paymentTerms: 'Net 30',
    status: 'Active',
    notes: ''
  }
}

function validateForm() {
  const f = form.value
  const errors = []

  console.log('Validating form:', f) // Debug log

  // Email validation
  if (!f.email || f.email.trim() === '') {
    errors.push('Email is required')
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(f.email.trim())) {
    errors.push('Please enter a valid email address')
  }

  // Phone validation
  if (!f.phone || f.phone.trim() === '') {
    errors.push('Phone number is required')
  }

  // Type-specific validation
  if (f.type === 'individual') {
    if (!f.firstName || f.firstName.trim() === '') {
      errors.push('First name is required')
    }
    if (!f.lastName || f.lastName.trim() === '') {
      errors.push('Last name is required')
    }
  } else {
    if (!f.companyName || f.companyName.trim() === '') {
      errors.push('Company name is required')
    }
    if (!f.contactPerson || f.contactPerson.trim() === '') {
      errors.push('Contact person is required')
    }
  }

  console.log('Validation result:', { isValid: errors.length === 0, errors }) // Debug log

  return {
    isValid: errors.length === 0,
    errors
  }
}

async function submit() {
  console.log('Submit function called') // Debug log
  console.log('Current form data:', form.value) // Debug log
  console.log('Auth state:', { isAuthenticated: authStore.isAuthenticated, session: !!authStore.session }) // Debug log

  // Check authentication
  if (!authStore.isAuthenticated || !authStore.session) {
    toast.error('You must be logged in to create a client')
    return
  }

  // Small delay to ensure form data is properly updated
  await new Promise(resolve => setTimeout(resolve, 100))

  const validation = validateForm()
  if (!validation.isValid) {
    console.log('Validation failed:', validation.errors) // Debug log
    toast.error(`Validation Error: ${validation.errors.join(', ')}`)
    return
  }

  try {
    isSubmitting.value = true
    console.log('Submitting client data...') // Debug log

    // Prepare client data for backend
    const clientData = {
      type: form.value.type === 'individual' ? 'Individual' : 'Business',
      name: form.value.type === 'individual'
        ? `${form.value.firstName.trim()} ${form.value.lastName.trim()}`.trim()
        : form.value.companyName.trim(),
      email: form.value.email.trim(),
      phone: form.value.phone.trim(),
      company: form.value.type === 'business' ? {
        name: form.value.companyName.trim(),
        website: form.value.website?.trim() || '',
        taxId: form.value.gstNumber?.trim() || ''
      } : undefined,
      address: {
        street: form.value.address.street?.trim() || '',
        city: form.value.address.city?.trim() || '',
        state: form.value.address.state?.trim() || '',
        zipCode: form.value.address.zipCode?.trim() || '',
        country: form.value.address.country || 'India'
      },
      currency: form.value.currency,
      paymentTerms: form.value.paymentTerms,
      status: form.value.status as 'Active' | 'Inactive' | 'Suspended',
      notes: form.value.notes?.trim() || ''
    }

    console.log('Client data prepared:', clientData) // Debug log

    if (props.client?._id) {
      // Update existing client
      await clientStore.updateClient(props.client._id, clientData)
      toast.success('Client updated successfully!')
    } else {
      // Create new client
      await clientStore.createClient(clientData)
      toast.success('Client created successfully!')
    }

    emit('client-saved')
    emit('close')
    resetForm()

    // Clear any search fields to prevent auto-population
    // This ensures the search field doesn't get filled with the client's email
    clearSearchInputs()
  } catch (error: any) {
    console.error('Submit error:', error) // Debug log
    toast.error(error.message || 'Failed to save client')
  } finally {
    isSubmitting.value = false
  }
}

function onDialogUpdate(open: boolean) {
  if (!open) {
    emit('close')
    resetForm()
  }
}

// Watch for client prop changes (for editing)
watch(() => props.client, (newClient) => {
  if (newClient && props.open) {
    // Populate form with existing client data
    const isBusinessClient = !!newClient.company?.name

    form.value = {
      type: isBusinessClient ? 'business' : 'individual',
      email: newClient.email || '',
      phone: newClient.phone || '',
      firstName: isBusinessClient ? '' : newClient.name?.split(' ')[0] || '',
      lastName: isBusinessClient ? '' : newClient.name?.split(' ').slice(1).join(' ') || '',
      companyName: newClient.company?.name || '',
      contactPerson: isBusinessClient ? newClient.name || '' : '',
      gstNumber: newClient.company?.taxId || '',
      website: newClient.company?.website || '',
      address: {
        street: newClient.address?.street || '',
        city: newClient.address?.city || '',
        state: newClient.address?.state || '',
        zipCode: newClient.address?.zipCode || '',
        country: newClient.address?.country || 'India'
      },
      currency: newClient.currency || 'INR',
      paymentTerms: newClient.paymentTerms || 'Net 30',
      status: newClient.status || 'Active',
      notes: newClient.notes || ''
    }
  }
}, { immediate: true })

// Watch for open prop changes
watch(() => props.open, (isOpen) => {
  if (!isOpen) {
    resetForm()
  }
})
</script>

<style scoped>
/* Hide scrollbar but allow scrolling */
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}
.scrollbar-hide {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}
</style>
