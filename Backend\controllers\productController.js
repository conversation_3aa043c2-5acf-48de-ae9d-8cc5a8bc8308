const Product = require('../models/Product');
const mongoose = require('mongoose');

/**
 * @desc    Get all products for authenticated user
 * @route   GET /api/products
 * @access  Private (Protected by Supabase JWT)
 */
const getProducts = async (req, res) => {
  try {
    const userId = req.user.id;
    const {
      page = 1,
      limit = 10,
      search,
      status,
      category,
      sort = '-createdAt',
      lowStock
    } = req.query;

    const options = {
      status,
      category,
      search,
      sort,
      lowStock: lowStock === 'true',
      limit: parseInt(limit),
      skip: (parseInt(page) - 1) * parseInt(limit)
    };

    // Get products with pagination
    const products = await Product.findByUser(userId, options);
    
    // Build count query
    let countQuery = { userId };
    if (status && status !== 'all') countQuery.status = status;
    if (category && category !== 'all') countQuery.category = category;
    if (search) {
      countQuery.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { productId: { $regex: search, $options: 'i' } },
        { sku: { $regex: search, $options: 'i' } }
      ];
    }
    if (lowStock === 'true') {
      countQuery.$expr = { $lte: ['$stock', '$minStockLevel'] };
    }

    const totalProducts = await Product.countDocuments(countQuery);
    const totalPages = Math.ceil(totalProducts / parseInt(limit));

    res.status(200).json({
      success: true,
      count: products.length,
      totalProducts,
      totalPages,
      currentPage: parseInt(page),
      data: products,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages,
        totalProducts,
        hasNextPage: parseInt(page) < totalPages,
        hasPrevPage: parseInt(page) > 1
      }
    });
  } catch (error) {
    console.error('Get products error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve products',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Get single product by ID
 * @route   GET /api/products/:id
 * @access  Private (Protected by Supabase JWT)
 */
const getProduct = async (req, res) => {
  try {
    const userId = req.user.id;
    const productId = req.params.id;

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(productId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid product ID format'
      });
    }

    const product = await Product.findOne({ 
      _id: productId, 
      userId 
    });

    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    res.status(200).json({
      success: true,
      data: product
    });
  } catch (error) {
    console.error('Get product error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve product',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Create new product
 * @route   POST /api/products
 * @access  Private (Protected by Supabase JWT)
 */
const createProduct = async (req, res) => {
  try {
    const userId = req.user.id;
    
    // Add userId to the product data
    const productData = {
      ...req.body,
      userId
    };

    // Check for duplicate SKU if provided
    if (productData.sku) {
      const existingProduct = await Product.findOne({
        userId,
        sku: productData.sku
      });

      if (existingProduct) {
        return res.status(400).json({
          success: false,
          message: 'Product with this SKU already exists'
        });
      }
    }

    const product = new Product(productData);
    const savedProduct = await product.save();

    res.status(201).json({
      success: true,
      message: 'Product created successfully',
      data: savedProduct
    });
  } catch (error) {
    console.error('Create product error:', error);
    
    // Handle validation errors
    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map(err => err.message);
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: messages
      });
    }

    // Handle duplicate key error
    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: 'Product with this SKU already exists'
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to create product',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Update product
 * @route   PUT /api/products/:id
 * @access  Private (Protected by Supabase JWT)
 */
const updateProduct = async (req, res) => {
  try {
    const userId = req.user.id;
    const productId = req.params.id;

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(productId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid product ID format'
      });
    }

    // Check if product exists and belongs to user
    const existingProduct = await Product.findOne({ 
      _id: productId, 
      userId 
    });

    if (!existingProduct) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    // Check for SKU conflicts if SKU is being updated
    if (req.body.sku && req.body.sku !== existingProduct.sku) {
      const skuConflict = await Product.findOne({
        userId,
        sku: req.body.sku,
        _id: { $ne: productId }
      });

      if (skuConflict) {
        return res.status(400).json({
          success: false,
          message: 'Another product with this SKU already exists'
        });
      }
    }

    // Update product
    const updatedProduct = await Product.findByIdAndUpdate(
      productId,
      { ...req.body, updatedAt: new Date() },
      { 
        new: true, 
        runValidators: true 
      }
    );

    res.status(200).json({
      success: true,
      message: 'Product updated successfully',
      data: updatedProduct
    });
  } catch (error) {
    console.error('Update product error:', error);
    
    // Handle validation errors
    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map(err => err.message);
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: messages
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to update product',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Delete product
 * @route   DELETE /api/products/:id
 * @access  Private (Protected by Supabase JWT)
 */
const deleteProduct = async (req, res) => {
  try {
    const userId = req.user.id;
    const productId = req.params.id;

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(productId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid product ID format'
      });
    }

    const product = await Product.findOneAndDelete({
      _id: productId,
      userId
    });

    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    res.status(200).json({
      success: true,
      message: 'Product deleted successfully',
      data: product
    });
  } catch (error) {
    console.error('Delete product error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete product',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Get product statistics
 * @route   GET /api/products/stats
 * @access  Private (Protected by Supabase JWT)
 */
const getProductStats = async (req, res) => {
  try {
    const userId = req.user.id;

    const stats = await Product.getStats(userId);

    const result = stats[0] || {
      totalProducts: 0,
      activeProducts: 0,
      inactiveProducts: 0,
      lowStockProducts: 0,
      totalStock: 0,
      totalValue: 0,
      totalRevenue: 0
    };

    res.status(200).json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('Get product stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve product statistics',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Get product categories
 * @route   GET /api/products/categories
 * @access  Private (Protected by Supabase JWT)
 */
const getProductCategories = async (_req, res) => {
  try {
    const categories = [
      'Electronics',
      'Clothing',
      'Books',
      'Home & Garden',
      'Sports & Outdoors',
      'Health & Beauty',
      'Automotive',
      'Office Supplies',
      'Software',
      'Digital Products',
      'Other'
    ];

    res.status(200).json({
      success: true,
      data: categories
    });
  } catch (error) {
    console.error('Get product categories error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve product categories',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Update product stock
 * @route   PUT /api/products/:id/stock
 * @access  Private (Protected by Supabase JWT)
 */
const updateProductStock = async (req, res) => {
  try {
    const userId = req.user.id;
    const productId = req.params.id;
    const { quantity, operation } = req.body; // operation: 'add' or 'subtract'

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(productId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid product ID format'
      });
    }

    if (!quantity || quantity <= 0) {
      return res.status(400).json({
        success: false,
        message: 'Quantity must be a positive number'
      });
    }

    if (!['add', 'subtract'].includes(operation)) {
      return res.status(400).json({
        success: false,
        message: 'Operation must be either "add" or "subtract"'
      });
    }

    const product = await Product.findOne({
      _id: productId,
      userId
    });

    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    // Calculate new stock
    let newStock;
    if (operation === 'add') {
      newStock = product.stock + quantity;
    } else {
      newStock = product.stock - quantity;
      if (newStock < 0) {
        return res.status(400).json({
          success: false,
          message: 'Insufficient stock. Cannot subtract more than available stock.'
        });
      }
    }

    // Update product stock
    const updatedProduct = await Product.findByIdAndUpdate(
      productId,
      { stock: newStock, updatedAt: new Date() },
      { new: true, runValidators: true }
    );

    res.status(200).json({
      success: true,
      message: `Product stock ${operation === 'add' ? 'increased' : 'decreased'} successfully`,
      data: updatedProduct
    });
  } catch (error) {
    console.error('Update product stock error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update product stock',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

module.exports = {
  getProducts,
  getProduct,
  createProduct,
  updateProduct,
  deleteProduct,
  getProductStats,
  getProductCategories,
  updateProductStock
};
