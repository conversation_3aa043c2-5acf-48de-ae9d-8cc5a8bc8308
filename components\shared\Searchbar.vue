<template>
    <div class="relative transition-all duration-300 ease-in-out">
      <div
        class="flex items-center border rounded-lg px-3 py-2 transition-all duration-300 ease-in-out bg-white shadow-sm focus-within:ring-2"
        :class="[`w-[${baseWidth}]`, `hover:w-[${hoverWidth}]`]"
        :style="`--tw-ring-color: ${ringColor}`"
      >
        <Icon :name="icon" class="w-5 h-5 text-gray-400" />
        <input
          type="text"
          :placeholder="currentPlaceholder"
          v-model="model"
          class="ml-2 outline-none w-[50px] bg-transparent text-sm text-gray-600"
        />
      </div>
    </div>
  </template>
  
  <script lang="ts" setup>
  import { ref, watchEffect, onMounted, onUnmounted } from 'vue'
  
  const props = defineProps<{
    modelValue: string
    placeholder?: string
    placeholderSuggestions?: string[]
    icon?: string
    ringColor?: string
    baseWidth?: string
    hoverWidth?: string
  }>()
  
  const emit = defineEmits(['update:modelValue'])
  const model = ref(props.modelValue)
  
  watchEffect(() => {
    emit('update:modelValue', model.value)
  })
  
  const currentPlaceholder = ref(props.placeholder || 'Search...')
  const suggestions = ref(props.placeholderSuggestions ?? [])
  let index = 0
  let interval: ReturnType<typeof setInterval>
  
  onMounted(() => {
    if (suggestions.value.length > 0) {
      interval = setInterval(() => {
        index = (index + 1) % suggestions.value.length
        currentPlaceholder.value = suggestions.value[index]
      }, 2000)
    }
  })
  
  onUnmounted(() => {
    clearInterval(interval)
  })
  </script>
  