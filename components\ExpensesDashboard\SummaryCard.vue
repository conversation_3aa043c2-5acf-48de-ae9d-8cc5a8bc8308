<template>
  <div class="bg-white p-6 border rounded-xl shadow flex items-center justify-between gap-4">
    <div>
      <p class="text-gray-500 text-sm">{{ title }}</p>
      <p class="text-xl font-bold">{{ value }}</p>
    </div>
    <component :is="icon" class="w-8 h-8" :class="iconColor" />
  </div>
</template>

<script setup>
const props = defineProps({
  title: String,
  value: String,
  icon: Object,
  iconColor: String,
})
</script>
