import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { invoiceApi, type Invoice, type InvoiceStats, type InvoiceParams } from '~/services/invoiceApi'

export const useInvoicesStore = defineStore('invoices', () => {
  // State
  const invoices = ref<Invoice[]>([])
  const currentInvoice = ref<Invoice | null>(null)
  const loading = ref(false)
  const error = ref<string>('')
  const stats = ref<InvoiceStats>({
    totalInvoices: 0,
    totalAmount: 0,
    paidAmount: 0,
    pendingAmount: 0,
    overdueAmount: 0,
    draftCount: 0,
    sentCount: 0,
    paidCount: 0,
    overdueCount: 0
  })

  // Pagination state
  const pagination = ref({
    page: 1,
    limit: 10,
    totalPages: 0,
    totalInvoices: 0,
    hasNextPage: false,
    hasPrevPage: false
  })

  // Filters
  const filters = ref({
    search: '',
    status: '',
    clientName: '',
    invoiceType: '',
    startDate: '',
    endDate: '',
    sort: '-createdAt'
  })

  // Computed
  const isLoading = computed(() => loading.value)
  const hasError = computed(() => !!error.value)
  const invoicesCount = computed(() => invoices.value.length)
  const draftInvoices = computed(() => invoices.value.filter(inv => inv.status === 'draft'))
  const sentInvoices = computed(() => invoices.value.filter(inv => inv.status === 'sent'))
  const paidInvoices = computed(() => invoices.value.filter(inv => inv.status === 'paid'))
  const overdueInvoices = computed(() => invoices.value.filter(inv => inv.status === 'overdue'))

  // Helper functions
  const setLoading = (value: boolean) => {
    loading.value = value
  }

  const setError = (message: string) => {
    error.value = message
  }

  const clearError = () => {
    error.value = ''
  }

  // Actions
  const fetchInvoices = async (params?: InvoiceParams) => {
    try {
      setLoading(true)
      clearError()

      const queryParams = {
        page: params?.page || pagination.value.page,
        limit: params?.limit || pagination.value.limit,
        search: params?.search || filters.value.search,
        status: params?.status || filters.value.status,
        clientName: params?.clientName || filters.value.clientName,
        invoiceType: params?.invoiceType || filters.value.invoiceType,
        startDate: params?.startDate || filters.value.startDate,
        endDate: params?.endDate || filters.value.endDate,
        sort: params?.sort || filters.value.sort
      }

      // Update filters
      if (params) {
        Object.assign(filters.value, params)
      }

      const response = await invoiceApi.getInvoices(queryParams)
      
      invoices.value = response.data
      if (response.pagination) {
        pagination.value = response.pagination
      }

      return response
    } catch (err: any) {
      setError(err.message || 'Failed to fetch invoices')
      throw err
    } finally {
      setLoading(false)
    }
  }

  const fetchInvoice = async (id: string) => {
    try {
      setLoading(true)
      clearError()

      console.log('🏪 Store: Fetching invoice with ID:', id)

      if (!id) {
        throw new Error('Invoice ID is required')
      }

      const response = await invoiceApi.getInvoice(id)
      console.log('🏪 Store: API response received:', response)

      if (!response || !response.data) {
        throw new Error('Invalid response from server')
      }

      currentInvoice.value = response.data

      console.log('🏪 Store: Invoice stored successfully:', {
        id: response.data._id,
        number: response.data.invoiceNumber,
        client: response.data.clientName,
        total: response.data.total,
        itemsCount: response.data.items?.length || 0
      })

      return response.data
    } catch (err: any) {
      console.error('🏪 Store: Error fetching invoice:', err)
      setError(err.message || 'Failed to fetch invoice')
      throw err
    } finally {
      setLoading(false)
    }
  }

  const createInvoice = async (invoiceData: Omit<Invoice, '_id' | 'userId'>) => {
    try {
      setLoading(true)
      clearError()

      const response = await invoiceApi.createInvoice(invoiceData)
      
      // Add to local state
      invoices.value.unshift(response.data)
      
      // Update stats
      await fetchStats()

      return response.data
    } catch (err: any) {
      setError(err.message || 'Failed to create invoice')
      throw err
    } finally {
      setLoading(false)
    }
  }

  const updateInvoice = async (id: string, invoiceData: Partial<Invoice>) => {
    try {
      setLoading(true)
      clearError()

      const response = await invoiceApi.updateInvoice(id, invoiceData)
      
      // Update local state
      const index = invoices.value.findIndex(inv => inv._id === id)
      if (index !== -1) {
        invoices.value[index] = response.data
      }
      
      // Update current invoice if it's the same
      if (currentInvoice.value?._id === id) {
        currentInvoice.value = response.data
      }

      // Update stats
      await fetchStats()

      return response.data
    } catch (err: any) {
      setError(err.message || 'Failed to update invoice')
      throw err
    } finally {
      setLoading(false)
    }
  }

  const deleteInvoice = async (id: string) => {
    try {
      setLoading(true)
      clearError()

      await invoiceApi.deleteInvoice(id)
      
      // Remove from local state
      invoices.value = invoices.value.filter(inv => inv._id !== id)
      
      // Clear current invoice if it's the same
      if (currentInvoice.value?._id === id) {
        currentInvoice.value = null
      }

      // Update stats
      await fetchStats()

      return true
    } catch (err: any) {
      setError(err.message || 'Failed to delete invoice')
      throw err
    } finally {
      setLoading(false)
    }
  }

  const fetchStats = async () => {
    try {
      const response = await invoiceApi.getInvoiceStats()
      stats.value = response.data
      return response.data
    } catch (err: any) {
      console.error('Failed to fetch invoice stats:', err)
      // Don't throw error for stats as it's not critical
    }
  }

  const updatePaymentStatus = async (invoiceId: string, isPaid: boolean) => {
    try {
      setLoading(true)
      clearError()

      const response = await invoiceApi.updatePaymentStatus(invoiceId, isPaid)

      // Update local state
      const index = invoices.value.findIndex(inv => inv._id === invoiceId)
      if (index !== -1) {
        invoices.value[index] = response.data
      }

      // Update current invoice if it's the same
      if (currentInvoice.value?._id === invoiceId) {
        currentInvoice.value = response.data
      }

      // Update stats
      await fetchStats()

      return response.data
    } catch (err: any) {
      setError(err.message || 'Failed to update payment status')
      throw err
    } finally {
      setLoading(false)
    }
  }

  const generatePDF = async (invoiceId: string) => {
    try {
      setLoading(true)
      clearError()

      // Get the invoice data first
      const invoice = invoices.value.find(inv => inv._id === invoiceId) || currentInvoice.value

      if (!invoice) {
        throw new Error('Invoice not found')
      }

      // Use the direct PDF downloader utility for immediate file download
      const { downloadInvoicePDFDirect } = await import('~/utils/directPdfDownloader')

      // Add a small delay to show loading state
      await new Promise(resolve => setTimeout(resolve, 500))

      await downloadInvoicePDFDirect(invoice, {
        filename: `Invoice-${invoice.invoiceNumber}.pdf`,
        format: 'a4',
        orientation: 'portrait'
      })

      // Success - PDF should be downloading now
      return {
        success: true,
        message: `PDF download started for ${invoice.invoiceNumber}`,
        invoice
      }
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to generate PDF'
      setError(errorMessage)
      throw new Error(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const printInvoice = async (invoiceId: string) => {
    try {
      setLoading(true)
      clearError()

      // Get the invoice data first
      const invoice = invoices.value.find(inv => inv._id === invoiceId) || currentInvoice.value

      if (!invoice) {
        throw new Error('Invoice not found')
      }

      // Use the print utility
      const { printInvoice } = await import('~/utils/printUtils')
      await printInvoice(invoice)

      return invoice
    } catch (err: any) {
      setError(err.message || 'Failed to print invoice')
      throw err
    } finally {
      setLoading(false)
    }
  }

  const sendEmail = async (invoiceId: string, emailData: { subject?: string; message?: string; recipientEmail?: string }) => {
    try {
      setLoading(true)
      clearError()

      const response = await invoiceApi.sendEmail(invoiceId, emailData)
      return response.data
    } catch (err: any) {
      setError(err.message || 'Failed to send email')
      throw err
    } finally {
      setLoading(false)
    }
  }

  const searchInvoices = async (searchTerm: string) => {
    filters.value.search = searchTerm
    pagination.value.page = 1 // Reset to first page
    await fetchInvoices()
  }

  const filterByStatus = async (status: string) => {
    filters.value.status = status
    pagination.value.page = 1 // Reset to first page
    await fetchInvoices()
  }

  const filterByClient = async (clientName: string) => {
    filters.value.clientName = clientName
    pagination.value.page = 1 // Reset to first page
    await fetchInvoices()
  }

  const fetchClientInvoices = async (clientName: string, params?: InvoiceParams) => {
    try {
      setLoading(true)
      clearError()

      const queryParams = {
        clientName,
        page: params?.page || 1,
        limit: params?.limit || 10,
        sort: params?.sort || '-createdAt',
        status: params?.status,
        invoiceType: params?.invoiceType,
        startDate: params?.startDate,
        endDate: params?.endDate
      }

      console.log('🏪 Store: Fetching invoices for client:', clientName, queryParams)

      const response = await invoiceApi.getInvoices(queryParams)

      console.log('🏪 Store: Client invoices response:', {
        client: clientName,
        count: response.data.length,
        pagination: response.pagination
      })

      return response
    } catch (err: any) {
      setError(err.message || 'Failed to fetch client invoices')
      throw err
    } finally {
      setLoading(false)
    }
  }

  const filterByDateRange = async (startDate: string, endDate: string) => {
    filters.value.startDate = startDate
    filters.value.endDate = endDate
    pagination.value.page = 1 // Reset to first page
    await fetchInvoices()
  }

  const filterByInvoiceType = async (invoiceType: string) => {
    filters.value.invoiceType = invoiceType
    pagination.value.page = 1 // Reset to first page
    await fetchInvoices()
  }

  const applyMultipleFilters = async (filterParams: Partial<typeof filters.value>) => {
    Object.assign(filters.value, filterParams)
    pagination.value.page = 1 // Reset to first page
    await fetchInvoices()
  }

  const sortInvoices = async (sortField: string) => {
    filters.value.sort = sortField
    pagination.value.page = 1 // Reset to first page
    await fetchInvoices()
  }

  const nextPage = async () => {
    if (pagination.value.hasNextPage) {
      pagination.value.page += 1
      await fetchInvoices()
    }
  }

  const prevPage = async () => {
    if (pagination.value.hasPrevPage) {
      pagination.value.page -= 1
      await fetchInvoices()
    }
  }

  const goToPage = async (page: number) => {
    pagination.value.page = page
    await fetchInvoices()
  }

  const refreshInvoices = async () => {
    await Promise.all([
      fetchInvoices(),
      fetchStats()
    ])
  }

  const clearCurrentInvoice = () => {
    currentInvoice.value = null
  }

  const resetFilters = async () => {
    filters.value = {
      search: '',
      status: '',
      clientName: '',
      invoiceType: '',
      startDate: '',
      endDate: '',
      sort: '-createdAt'
    }
    pagination.value.page = 1
    await fetchInvoices()
  }

  // Computed for active filters count
  const activeFiltersCount = computed(() => {
    let count = 0
    if (filters.value.search) count++
    if (filters.value.status) count++
    if (filters.value.clientName) count++
    if (filters.value.invoiceType) count++
    if (filters.value.startDate || filters.value.endDate) count++
    return count
  })

  // Computed for checking if any filters are active
  const hasActiveFilters = computed(() => activeFiltersCount.value > 0)

  return {
    // State
    invoices,
    currentInvoice,
    loading,
    error,
    stats,
    pagination,
    filters,

    // Computed
    isLoading,
    hasError,
    invoicesCount,
    draftInvoices,
    sentInvoices,
    paidInvoices,
    overdueInvoices,

    // Actions
    fetchInvoices,
    fetchInvoice,
    fetchClientInvoices,
    createInvoice,
    updateInvoice,
    deleteInvoice,
    fetchStats,
    updatePaymentStatus,
    generatePDF,
    printInvoice,
    sendEmail,
    searchInvoices,
    filterByStatus,
    filterByClient,
    filterByDateRange,
    filterByInvoiceType,
    applyMultipleFilters,
    sortInvoices,
    nextPage,
    prevPage,
    goToPage,
    refreshInvoices,
    clearCurrentInvoice,
    resetFilters,
    setError,
    clearError,

    // Computed
    activeFiltersCount,
    hasActiveFilters
  }
})
