// Test utility for product description field functionality
export const testProductDescriptionData = {
  // Test product data
  testProducts: [
    {
      _id: 'product-1',
      name: 'Premium Wireless Headphones',
      price: 2500,
      description: 'Noise-cancelling wireless headphones with 30-hour battery life',
      category: 'Electronics'
    },
    {
      _id: 'product-2',
      name: 'Ergonomic Office Chair',
      price: 15000,
      description: 'Adjustable height office chair with lumbar support',
      category: 'Furniture'
    }
  ],

  // Test invoice item data
  testItems: [
    {
      id: 1,
      type: 'product',
      description: 'Premium Wireless Headphones - Noise Cancelling, Bluetooth 5.0, 30hr Battery',
      quantity: 2,
      unitPrice: 2500,
      productId: 'product-1'
    },
    {
      id: 2,
      type: 'product',
      description: 'Ergonomic Office Chair - Adjustable Height, Lumbar Support, Mesh Back',
      quantity: 1,
      unitPrice: 15000,
      productId: 'product-2'
    }
  ]
}

// Test function to validate description field requirements
export const validateDescriptionField = (item: any): boolean => {
  console.log('🧪 Testing description field validation...')
  console.log(`📄 Item: ${JSON.stringify(item)}`)

  // Check required fields
  if (!item.description || !item.description.trim()) {
    console.error('❌ Description is required but missing')
    return false
  }

  if (item.description.trim().length < 3) {
    console.error('❌ Description too short (minimum 3 characters)')
    return false
  }

  if (item.quantity <= 0) {
    console.error('❌ Quantity must be greater than 0')
    return false
  }

  if (item.unitPrice <= 0) {
    console.error('❌ Unit price must be greater than 0')
    return false
  }

  console.log('✅ Description field validation passed')
  return true
}

// Test function to simulate product selection
export const simulateProductSelection = (products: any[], selectedProductId: string): any => {
  console.log('🔄 Simulating product selection...')
  console.log(`🎯 Selected product ID: ${selectedProductId}`)

  const product = products.find(p => p._id === selectedProductId)
  
  if (!product) {
    console.error('❌ Product not found')
    return null
  }

  const selectedItem = {
    id: Date.now(),
    type: 'product',
    description: product.name,
    quantity: 1,
    unitPrice: product.price,
    productId: product._id
  }

  console.log('✅ Product selection result:')
  console.log(`   - Description: ${selectedItem.description}`)
  console.log(`   - Unit Price: ₹${selectedItem.unitPrice}`)
  console.log(`   - Product ID: ${selectedItem.productId}`)

  return selectedItem
}

// Test function for manual description entry
export const testManualDescriptionEntry = (description: string): any => {
  console.log('✏️ Testing manual description entry...')
  console.log(`📝 Description: "${description}"`)

  const manualItem = {
    id: Date.now(),
    type: 'product',
    description: description,
    quantity: 1,
    unitPrice: 0,
    productId: ''
  }

  const isValid = validateDescriptionField({
    ...manualItem,
    unitPrice: 100 // Set a valid price for validation
  })

  console.log(`✅ Manual entry result: ${isValid ? 'Valid' : 'Invalid'}`)
  return manualItem
}

// Test function for invoice data structure
export const testInvoiceDataStructure = (items: any[]): any => {
  console.log('📊 Testing invoice data structure...')
  
  const invoiceData = {
    items: items.map(item => ({
      description: item.description,
      quantity: item.quantity,
      unitPrice: item.unitPrice,
      type: 'product',
      productId: item.productId || undefined
    })),
    subtotal: items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0),
    taxRate: 18,
    discountRate: 0
  }

  // Calculate totals
  invoiceData.taxAmount = (invoiceData.subtotal * invoiceData.taxRate) / 100
  invoiceData.discountAmount = (invoiceData.subtotal * invoiceData.discountRate) / 100
  invoiceData.total = invoiceData.subtotal - invoiceData.discountAmount + invoiceData.taxAmount

  console.log('📈 Invoice Data Structure:')
  console.log(`   - Items: ${invoiceData.items.length}`)
  console.log(`   - Subtotal: ₹${invoiceData.subtotal.toFixed(2)}`)
  console.log(`   - Tax: ₹${invoiceData.taxAmount.toFixed(2)}`)
  console.log(`   - Total: ₹${invoiceData.total.toFixed(2)}`)

  // Validate all items have descriptions
  const invalidItems = invoiceData.items.filter(item => !item.description.trim())
  if (invalidItems.length > 0) {
    console.error(`❌ ${invalidItems.length} items missing descriptions`)
    return false
  }

  console.log('✅ Invoice data structure is valid')
  return invoiceData
}

// Test function for PDF generation compatibility
export const testPDFCompatibility = (items: any[]): boolean => {
  console.log('📄 Testing PDF generation compatibility...')
  
  for (let i = 0; i < items.length; i++) {
    const item = items[i]
    
    // Check for required fields for PDF
    if (!item.description || !item.description.trim()) {
      console.error(`❌ Item ${i + 1}: Missing description for PDF`)
      return false
    }
    
    if (!item.quantity || item.quantity <= 0) {
      console.error(`❌ Item ${i + 1}: Invalid quantity for PDF`)
      return false
    }
    
    if (!item.unitPrice || item.unitPrice <= 0) {
      console.error(`❌ Item ${i + 1}: Invalid unit price for PDF`)
      return false
    }
    
    // Check description length for PDF formatting
    if (item.description.length > 100) {
      console.warn(`⚠️ Item ${i + 1}: Description may be too long for PDF (${item.description.length} chars)`)
    }
  }
  
  console.log('✅ All items are compatible with PDF generation')
  return true
}

// Comprehensive test suite
export const runProductDescriptionTests = (): void => {
  console.log('🧪 Running Product Description Tests...')
  console.log('==========================================')

  // Test 1: Product selection
  console.log('Test 1: Product Selection')
  const selectedProduct = simulateProductSelection(testProductDescriptionData.testProducts, 'product-1')
  const selectionValid = selectedProduct && validateDescriptionField(selectedProduct)
  console.log(`Result: ${selectionValid ? '✅ PASS' : '❌ FAIL'}`)
  console.log('')

  // Test 2: Manual description entry
  console.log('Test 2: Manual Description Entry')
  const manualItem = testManualDescriptionEntry('Custom Product - High Quality Material, Professional Grade')
  const manualValid = manualItem && manualItem.description.length > 0
  console.log(`Result: ${manualValid ? '✅ PASS' : '❌ FAIL'}`)
  console.log('')

  // Test 3: Empty description validation
  console.log('Test 3: Empty Description Validation')
  const emptyItem = { description: '', quantity: 1, unitPrice: 100 }
  const emptyValid = !validateDescriptionField(emptyItem) // Should fail
  console.log(`Result: ${emptyValid ? '✅ PASS' : '❌ FAIL'}`)
  console.log('')

  // Test 4: Invoice data structure
  console.log('Test 4: Invoice Data Structure')
  const invoiceData = testInvoiceDataStructure(testProductDescriptionData.testItems)
  const structureValid = invoiceData && invoiceData.items.length > 0
  console.log(`Result: ${structureValid ? '✅ PASS' : '❌ FAIL'}`)
  console.log('')

  // Test 5: PDF compatibility
  console.log('Test 5: PDF Generation Compatibility')
  const pdfValid = testPDFCompatibility(testProductDescriptionData.testItems)
  console.log(`Result: ${pdfValid ? '✅ PASS' : '❌ FAIL'}`)
  console.log('')

  console.log('==========================================')
  console.log('🏁 Product Description Tests Complete!')
}

// Export for browser console testing
if (typeof window !== 'undefined') {
  (window as any).testProductDescription = runProductDescriptionTests
  (window as any).validateDescriptionField = validateDescriptionField
  (window as any).simulateProductSelection = simulateProductSelection
}
