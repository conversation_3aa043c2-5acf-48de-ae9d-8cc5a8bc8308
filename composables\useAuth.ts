// composables/useAuth.ts
import { useAuthStore } from '~/stores/auth'

export const useAuth = () => {
  const authStore = useAuthStore()
  const router = useRouter()

  const login = async (email: string, password: string) => {
    const supabase = useSupabaseClient()
    
    try {
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password
      })

      if (error) {
        throw error
      }

      // Fetch user data after successful login
      await authStore.fetchUser()
      
      return { success: true }
    } catch (error: any) {
      console.error('Login error:', error)
      return { 
        success: false, 
        error: error.message || 'Login failed' 
      }
    }
  }

  const logout = async () => {
    try {
      await authStore.logout()
      return { success: true }
    } catch (error: any) {
      console.error('Logout error:', error)
      return { 
        success: false, 
        error: error.message || 'Logout failed' 
      }
    }
  }

  const register = async (email: string, password: string, metadata?: any) => {
    const supabase = useSupabaseClient()
    
    try {
      const { error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: metadata
        }
      })

      if (error) {
        throw error
      }

      return { success: true }
    } catch (error: any) {
      console.error('Registration error:', error)
      return { 
        success: false, 
        error: error.message || 'Registration failed' 
      }
    }
  }

  const resetPassword = async (email: string) => {
    const supabase = useSupabaseClient()
    
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/Auth/reset-password`,
      })

      if (error) {
        throw error
      }

      return { success: true }
    } catch (error: any) {
      console.error('Password reset error:', error)
      return { 
        success: false, 
        error: error.message || 'Password reset failed' 
      }
    }
  }

  const updatePassword = async (password: string) => {
    const supabase = useSupabaseClient()
    
    try {
      const { error } = await supabase.auth.updateUser({
        password
      })

      if (error) {
        throw error
      }

      return { success: true }
    } catch (error: any) {
      console.error('Password update error:', error)
      return { 
        success: false, 
        error: error.message || 'Password update failed' 
      }
    }
  }

  const refreshSession = async () => {
    try {
      const result = await authStore.refreshSession()
      return { success: result }
    } catch (error: any) {
      console.error('Session refresh error:', error)
      return { 
        success: false, 
        error: error.message || 'Session refresh failed' 
      }
    }
  }

  return {
    // State
    user: computed(() => authStore.user),
    session: computed(() => authStore.session),
    isAuthenticated: computed(() => authStore.isAuthenticated),
    loading: computed(() => authStore.loading),
    userEmail: computed(() => authStore.userEmail),
    userName: computed(() => authStore.userName),
    
    // Actions
    login,
    logout,
    register,
    resetPassword,
    updatePassword,
    refreshSession,
    
    // Store methods
    fetchUser: authStore.fetchUser
  }
}
