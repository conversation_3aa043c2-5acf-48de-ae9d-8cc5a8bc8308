<template>
  <div class="space-y-6">
    <!-- Loading State -->
    <div v-if="clientStore.isLoading" class="flex items-center justify-center py-12">
      <Icon name="lucide:loader-2" class="w-8 h-8 animate-spin text-[#00C951]" />
      <span class="ml-2 text-gray-600">Loading client details...</span>
    </div>

    <!-- Error State -->
    <div v-else-if="clientStore.hasError" class="text-center py-12">
      <Icon name="lucide:alert-circle" class="w-12 h-12 mx-auto mb-4 text-red-500" />
      <h2 class="text-xl font-semibold text-gray-900 mb-2">Error Loading Client</h2>
      <p class="text-gray-600 mb-4">{{ clientStore.error }}</p>
      <Button @click="loadClient" variant="outline">
        <Icon name="lucide:refresh-cw" class="w-4 h-4 mr-2" />
        Try Again
      </Button>
    </div>

    <!-- Client Details -->
    <div v-else-if="client" class="space-y-6">
      <!-- Header -->
      <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div class="flex items-center gap-4">
          <Button variant="ghost" @click="$router.back()" class="p-2">
            <Icon name="lucide:arrow-left" class="w-5 h-5" />
          </Button>
          <div>
            <h1 class="text-2xl font-bold text-gray-900">{{ client.name }}</h1>
            <p class="text-gray-600">{{ client.company?.name || 'Individual Client' }}</p>
          </div>
        </div>
        <!-- Actions removed from client overview page as per requirements -->
      </div>

      <!-- Status Badge -->
      <div>
        <span class="px-3 py-1 text-sm font-medium rounded-full" :class="getStatusClass(client.status)">
          {{ client.status }}
        </span>
      </div>

      <!-- Client Information Cards -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Contact Information -->
        <Card>
          <CardHeader>
            <CardTitle class="flex items-center gap-2">
              <Icon name="lucide:contact" class="w-5 h-5" />
              Contact Information
            </CardTitle>
          </CardHeader>
          <CardContent class="space-y-4">
            <div>
              <label class="text-sm font-medium text-gray-500">Client ID</label>
              <p class="text-gray-900 font-mono">{{ client.clientId || 'Generating...' }}</p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Email</label>
              <p class="text-gray-900">{{ client.email }}</p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Phone</label>
              <p class="text-gray-900">{{ client.phone || 'Not provided' }}</p>
            </div>
            <div v-if="client.address">
              <label class="text-sm font-medium text-gray-500">Address</label>
              <p class="text-gray-900">{{ formatAddress(client.address) }}</p>
            </div>
          </CardContent>
        </Card>

        <!-- Company Information -->
        <Card v-if="client.company">
          <CardHeader>
            <CardTitle class="flex items-center gap-2">
              <Icon name="lucide:building" class="w-5 h-5" />
              Company Information
            </CardTitle>
          </CardHeader>
          <CardContent class="space-y-4">
            <div>
              <label class="text-sm font-medium text-gray-500">Company Name</label>
              <p class="text-gray-900">{{ client.company.name }}</p>
            </div>
            <div v-if="client.company.website">
              <label class="text-sm font-medium text-gray-500">Website</label>
              <a :href="client.company.website" target="_blank" class="text-[#00C951] hover:underline">
                {{ client.company.website }}
              </a>
            </div>
            <div v-if="client.company.taxId">
              <label class="text-sm font-medium text-gray-500">Tax ID</label>
              <p class="text-gray-900">{{ client.company.taxId }}</p>
            </div>
          </CardContent>
        </Card>

        <!-- Financial Information -->
        <Card>
          <CardHeader>
            <CardTitle class="flex items-center gap-2">
              <Icon name="lucide:credit-card" class="w-5 h-5" />
              Financial Information
            </CardTitle>
          </CardHeader>
          <CardContent class="space-y-4">
            <div>
              <label class="text-sm font-medium text-gray-500">Currency</label>
              <p class="text-gray-900">{{ client.currency || 'INR' }}</p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Payment Terms</label>
              <p class="text-gray-900">{{ client.paymentTerms || 'Net 30' }}</p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Credit Limit</label>
              <p class="text-gray-900">₹{{ formatCurrency(client.creditLimit || 0) }}</p>
            </div>
          </CardContent>
        </Card>

        <!-- Invoice Statistics -->
        <Card>
          <CardHeader>
            <CardTitle class="flex items-center gap-2">
              <Icon name="lucide:bar-chart" class="w-5 h-5" />
              Invoice Statistics
            </CardTitle>
          </CardHeader>
          <CardContent class="space-y-4">
            <div>
              <label class="text-sm font-medium text-gray-500">Total Invoices</label>
              <p class="text-gray-900">{{ client.totalInvoices || 0 }}</p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Total Amount</label>
              <p class="text-gray-900">₹{{ formatCurrency(client.totalAmount || 0) }}</p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Outstanding Amount</label>
              <p class="text-gray-900">₹{{ formatCurrency(client.outstandingAmount || 0) }}</p>
            </div>
            <div v-if="client.lastInvoiceDate">
              <label class="text-sm font-medium text-gray-500">Last Invoice</label>
              <p class="text-gray-900">{{ formatDate(client.lastInvoiceDate) }}</p>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- Notes -->
      <Card v-if="client.notes">
        <CardHeader>
          <CardTitle class="flex items-center gap-2">
            <Icon name="lucide:sticky-note" class="w-5 h-5" />
            Notes
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p class="text-gray-900 whitespace-pre-wrap">{{ client.notes }}</p>
        </CardContent>
      </Card>

      <!-- Tags -->
      <Card v-if="client.tags && client.tags.length > 0">
        <CardHeader>
          <CardTitle class="flex items-center gap-2">
            <Icon name="lucide:tags" class="w-5 h-5" />
            Tags
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div class="flex flex-wrap gap-2">
            <span 
              v-for="tag in client.tags" 
              :key="tag"
              class="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full"
            >
              {{ tag }}
            </span>
          </div>
        </CardContent>
      </Card>

      <!-- Timestamps -->
      <Card>
        <CardHeader>
          <CardTitle class="flex items-center gap-2">
            <Icon name="lucide:clock" class="w-5 h-5" />
            Timeline
          </CardTitle>
        </CardHeader>
        <CardContent class="space-y-4">
          <div>
            <label class="text-sm font-medium text-gray-500">Created</label>
            <p class="text-gray-900">{{ formatDate(client.createdAt) }}</p>
          </div>
          <div>
            <label class="text-sm font-medium text-gray-500">Last Updated</label>
            <p class="text-gray-900">{{ formatDate(client.updatedAt) }}</p>
          </div>
        </CardContent>
      </Card>

      <!-- Client Invoices Section -->
      <Card>
        <CardHeader>
          <CardTitle class="flex items-center gap-2">
            <Icon name="lucide:file-text" class="w-5 h-5" />
            Client Invoices
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ClientInvoiceList :client-name="client.name" />
        </CardContent>
      </Card>
    </div>

    <!-- Edit/Delete modals removed from client overview page as per requirements -->
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useClientStore } from '~/stores/clients'

definePageMeta({
  middleware: 'auth'
})

const route = useRoute()
const clientStore = useClientStore()

const clientId = computed(() => route.params.id as string)
const client = computed(() => clientStore.currentClient)

// Helper functions
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-IN').format(amount || 0)
}

const formatDate = (date: string | undefined) => {
  if (!date) return 'N/A'
  return new Date(date).toLocaleDateString('en-IN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const formatAddress = (address: any) => {
  if (!address) return 'Not provided'
  
  const parts = [
    address.street,
    address.city,
    address.state,
    address.zipCode,
    address.country
  ].filter(Boolean)
  
  return parts.join(', ') || 'Not provided'
}

const getStatusClass = (status: string) => {
  switch (status) {
    case 'Active':
      return 'bg-green-100 text-green-800'
    case 'Inactive':
      return 'bg-red-100 text-red-800'
    case 'Suspended':
      return 'bg-yellow-100 text-yellow-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

// Actions
const loadClient = async () => {
  try {
    await clientStore.fetchClient(clientId.value)
  } catch (error: any) {
    console.error('Failed to load client:', error.message)
  }
}

// Edit/delete functions removed from client overview page as per requirements

// Initialize
onMounted(async () => {
  await loadClient()
})

// Watch for route changes to reload client data
watch(() => route.params.id, async (newId: string) => {
  if (newId) {
    await loadClient()
  }
})
</script>
