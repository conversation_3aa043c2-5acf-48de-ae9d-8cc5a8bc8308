// Test script to verify invoice view functionality
// This script will test the complete flow from invoice creation to viewing

const testInvoiceView = async () => {
  console.log('🧪 Starting Invoice View Test...')
  
  // Test data
  const testInvoice = {
    clientName: 'Test Client',
    clientEmail: '<EMAIL>',
    clientPhone: '1234567890',
    clientAddress: {
      street: '123 Test Street',
      city: 'Test City',
      state: 'Test State',
      zipCode: '12345',
      country: 'Test Country'
    },
    items: [
      {
        description: 'Test Product 1',
        quantity: 2,
        unitPrice: 100,
        type: 'product'
      },
      {
        description: 'Test Service 1',
        quantity: 1,
        unitPrice: 200,
        type: 'service'
      }
    ],
    taxRate: 10,
    discountRate: 5,
    dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days from now
    notes: 'This is a test invoice for debugging purposes',
    invoiceType: 'mixed',
    status: 'sent'
  }

  try {
    // Step 1: Create a test invoice
    console.log('📝 Step 1: Creating test invoice...')
    const createResponse = await fetch('http://localhost:5000/api/invoices', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-token' // You'll need to replace this with a valid token
      },
      body: JSON.stringify(testInvoice)
    })

    if (!createResponse.ok) {
      throw new Error(`Failed to create invoice: ${createResponse.status}`)
    }

    const createdInvoice = await createResponse.json()
    console.log('✅ Invoice created:', createdInvoice.data)

    // Step 2: Fetch the invoice by ID
    console.log('🔍 Step 2: Fetching invoice by ID...')
    const fetchResponse = await fetch(`http://localhost:5000/api/invoices/${createdInvoice.data._id}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-token' // You'll need to replace this with a valid token
      }
    })

    if (!fetchResponse.ok) {
      throw new Error(`Failed to fetch invoice: ${fetchResponse.status}`)
    }

    const fetchedInvoice = await fetchResponse.json()
    console.log('✅ Invoice fetched:', fetchedInvoice.data)

    // Step 3: Verify data integrity
    console.log('🔍 Step 3: Verifying data integrity...')
    const invoice = fetchedInvoice.data

    // Check client data
    if (!invoice.clientName || !invoice.clientEmail) {
      console.error('❌ Client data missing:', {
        clientName: invoice.clientName,
        clientEmail: invoice.clientEmail
      })
    } else {
      console.log('✅ Client data present')
    }

    // Check items
    if (!invoice.items || invoice.items.length === 0) {
      console.error('❌ Items missing')
    } else {
      console.log('✅ Items present:', invoice.items.length)
    }

    // Check calculations
    if (!invoice.total && invoice.total !== 0) {
      console.error('❌ Total amount missing:', invoice.total)
    } else {
      console.log('✅ Total amount present:', invoice.total)
    }

    // Check subtotal calculation
    const expectedSubtotal = invoice.items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0)
    if (Math.abs(invoice.subtotal - expectedSubtotal) > 0.01) {
      console.error('❌ Subtotal calculation incorrect:', {
        expected: expectedSubtotal,
        actual: invoice.subtotal
      })
    } else {
      console.log('✅ Subtotal calculation correct')
    }

    // Check discount calculation
    const expectedDiscountAmount = (expectedSubtotal * invoice.discountRate) / 100
    if (Math.abs(invoice.discountAmount - expectedDiscountAmount) > 0.01) {
      console.error('❌ Discount calculation incorrect:', {
        expected: expectedDiscountAmount,
        actual: invoice.discountAmount
      })
    } else {
      console.log('✅ Discount calculation correct')
    }

    // Check tax calculation
    const taxableAmount = expectedSubtotal - expectedDiscountAmount
    const expectedTaxAmount = (taxableAmount * invoice.taxRate) / 100
    if (Math.abs(invoice.taxAmount - expectedTaxAmount) > 0.01) {
      console.error('❌ Tax calculation incorrect:', {
        expected: expectedTaxAmount,
        actual: invoice.taxAmount
      })
    } else {
      console.log('✅ Tax calculation correct')
    }

    // Check total calculation
    const expectedTotal = expectedSubtotal - expectedDiscountAmount + expectedTaxAmount
    if (Math.abs(invoice.total - expectedTotal) > 0.01) {
      console.error('❌ Total calculation incorrect:', {
        expected: expectedTotal,
        actual: invoice.total
      })
    } else {
      console.log('✅ Total calculation correct')
    }

    console.log('🎉 Invoice View Test Completed Successfully!')
    console.log('📊 Test Results Summary:', {
      invoiceId: invoice._id,
      invoiceNumber: invoice.invoiceNumber,
      clientName: invoice.clientName,
      itemsCount: invoice.items.length,
      subtotal: invoice.subtotal,
      discountAmount: invoice.discountAmount,
      taxAmount: invoice.taxAmount,
      total: invoice.total
    })

    return invoice._id

  } catch (error) {
    console.error('❌ Test failed:', error)
    throw error
  }
}

// Export for use in browser console
if (typeof window !== 'undefined') {
  window.testInvoiceView = testInvoiceView
}

// Run if in Node.js environment
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { testInvoiceView }
}

console.log('🧪 Test script loaded. Run testInvoiceView() to start the test.')
