<template>
  <!-- Services Card -->
  <div class="bg-white text-black rounded-2xl p-4 flex justify-between items-center shadow-sm border border-gray-200 hover:shadow-lg transition-shadow duration-200">
    <div>
      <h2 class="text-gray-600 text-sm font-semibold">Services</h2>
      <div class="text-3xl font-bold text-[#00C951]">
        {{ dashboardStore.totalServices }}
      </div>
      <p class="text-sm text-gray-400 mt-1">
        {{ dashboardStore.activeServices }} active services
      </p>
    </div>
    <div class="p-1 bg-green-200 rounded-full">
      <Icon name="lucide:briefcase" class="w-10 h-10 text-[#05DF72]"/>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useDashboardStore } from '~/stores/dashboard'

const dashboardStore = useDashboardStore()
</script>

<style>

</style>