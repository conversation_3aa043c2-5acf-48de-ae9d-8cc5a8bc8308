<template>
     <!-- Heading -->
      <Heading
    title="Send Payment Request"
    description="Track and manage all Payment Request ."
    icon="lucide:indian-rupee"
    iconColor="text-green-400"
    bgColor="bg-white"
    :buttons="[
      {
        label: 'Cancel',
        icon: 'lucide:minus',
        bgColor: 'bg-[#00C951]',
        textColor: 'text-white',
        to: '/payments/PaymentRequests'
      },
    ]"
  />
  <div class="p-6 min-h-screen bg-[#f9fbfc]">
    <!-- Card -->
    <div class="bg-white p-6 rounded-xl shadow-md max-w-4xl mx-auto border">
      <!-- Title -->
      <div class="flex items-center gap-2 mb-6">
        <Send class="w-5 h-5 text-green-400" />
        <h2 class="text-lg font-semibold text-gray-800">Payment Request Details</h2>
      </div>

      <!-- Form -->
      <form @submit.prevent="handleSubmit" class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <!-- Pending Invoice -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Pending Invoice</label>
          <select
            v-model="form.invoice"
            class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500"
          >
            <option disabled selected>Select pending invoice</option>
            <option>INV-001</option>
            <option>INV-002</option>
            <option>INV-003</option>
          </select>
        </div>

        <!-- Client -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Client</label>
          <select
            v-model="form.client"
            class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500"
          >
            <option disabled selected>Select client</option>
            <option>Tech Solutions - <EMAIL></option>
            <option>StartupX - <EMAIL></option>
          </select>
        </div>

        <!-- Amount -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Amount (₹)</label>
          <div class="relative">
            <span class="absolute left-3 top-2.5 text-gray-500 text-sm">₹</span>
            <input
              type="number"
              v-model="form.amount"
              class="pl-7 pr-3 py-2 border border-gray-300 rounded-md w-full text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500"
              placeholder="0"
            />
          </div>
        </div>

        <!-- Due Date -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Due Date</label>
          <div class="relative">
            <Clock class="absolute left-3 top-2.5 w-4 h-4 text-gray-400" />
            <input
              type="date"
              v-model="form.dueDate"
              class="pl-9 pr-3 py-2 border border-gray-300 rounded-md w-full text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500"
            />
          </div>
        </div>

        <!-- UPI ID -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">UPI ID</label>
          <input
            type="text"
            v-model="form.upiId"
            class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500"
            placeholder="business@upi"
          />
        </div>

        <!-- Business Name -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Business Name</label>
          <input
            type="text"
            v-model="form.businessName"
            class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500"
            placeholder="Quote@Easy"
          />
        </div>

        <!-- Custom Message -->
        <div class="md:col-span-2">
          <label class="block text-sm font-medium text-gray-700 mb-1">Custom Message (Optional)</label>
          <textarea
            v-model="form.message"
            rows="3"
            class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500"
            placeholder="Add a personal message for the client"
          ></textarea>
        </div>

        <!-- Submit Button -->
        <div class="md:col-span-2">
          <Button
            type="submit"
            class="w-full text-white rounded-md py-2 font-medium transition"
            variant="premium"
          >
            Generate Payment Link
          </Button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { Send, Clock } from 'lucide-vue-next'

const form = ref({
  invoice: '',
  client: '',
  amount: '',
  dueDate: '',
  upiId: 'business@upi',
  businessName: 'Quote@Easy',
  message: '',
})

const handleSubmit = () => {
  console.log('Form submitted:', form.value)
}
</script>
