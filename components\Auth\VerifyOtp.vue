<template>
  <div>
    <Navbar/>
  </div>
    <section class="h-fit bg-gray-100 font-serif">
      <div class="flex min-h-full flex-1 flex-col justify-center px-6 py-12 lg:px-8">
        <div class="sm:mx-auto sm:w-full sm:max-w-sm">
          <img class="mx-auto h-10 w-auto" src="/assets/invoice-easy-logo.png" alt="invoice-logo" >
          <h2 class="mt-3 text-center text-2xl font-bold tracking-tight text-black">
            Verify OTP
          </h2>
          <p class="mt-3 text-center text-black">Enter the OTP sent to your email and phone</p>
        </div>
  
        <div class="mt-3 sm:mx-auto sm:w-full sm:max-w-sm">
          <div class="border border-gray-300 bg-white shadow-lg z-50 p-6 rounded-xl">
            <div class="space-y-6">
              <!-- Form Fields -->
              <div>
                <label for="Email" class="block text-sm font-medium text-black">Email Address</label>
                <div class="mt-2">
                  <input
                    v-model="otpData.email"
                    type="email"
                    name="email"
                    class="block w-full rounded-md bg-white px-3 py-1.5 text-base text-black outline-1 outline-gray-300 focus:outline-2 focus:outline-[#00C951]"
                    placeholder="Enter your email"
                    required
                  >
                </div>
              </div>
  
              <!-- <div>
                <label for="Phone" class="block text-sm font-medium text-black">Phone</label>
                <div class="mt-2">
                  <input
                    v-model="otpData.phone"
                    type="tel"
                    name="phone"
                    class="block w-full rounded-md bg-white px-3 py-1.5 text-base text-black outline-1 outline-gray-300 focus:outline-2 focus:outline-[#00C951]"
                    placeholder="Enter your phone number"
                    maxlength="13"
                    required
                  >
                </div>
              </div> -->
  
              <div>
                <label for="OTP" class="block text-sm font-medium text-black">OTP</label>
                <div class="mt-2">
                  <input
                    v-model="otpData.otp"
                    type="text"
                    name="otp"
                    class="block w-full rounded-md bg-white px-3 py-1.5 text-base text-black outline-1 outline-gray-300 focus:outline-2 focus:outline-[#00C951]"
                    placeholder="Enter the OTP"
                    required
                  >
                </div>
              </div>
  
              <div>
                <button type="button" class="w-full rounded-md bg-[#00C951] px-3 py-1.5 text-sm font-semibold text-white shadow-xs hover:bg-[#023430] focus:outline-2 focus:outline-[#00C951]" @click="verifyOtp">
                  Verify OTP
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </template>
  
  <script setup>
  import { ref } from 'vue';
  import { useVerifyOtpStore } from '@/stores/verifyOtp'; // Adjust the path based on your folder structure
  import { useRouter } from 'vue-router'; // Import the router
  
  const verifyOtpStore = useVerifyOtpStore();
  const router = useRouter(); // Initialize the router
  
  const otpData = ref({
    email: '',
    phone: '',
    otp: '',
  });
  
  const verifyOtp = async () => {
    // Ensure the phone number includes the country code
    otpData.value.phone = `+91${otpData.value.phone}`;
  
    // Set the otpData in the verifyOtpStore before calling verifyOtp
    verifyOtpStore.setOtpData(otpData.value);
  
    const { success, message } = await verifyOtpStore.verifyOtp();
  
    if (success) {
      // Redirect user to the dashboard or another page
      router.push('/dashboard');
    } else {
      console.error('OTP verification failed:', message);
    }
  };
  </script>
  