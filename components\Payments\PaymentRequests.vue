<template>
  <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 p-6">
    <!-- Total Requests -->
    <div class="bg-white shadow-md p-6 rounded-xl flex justify-between items-center border">
      <div>
        <p class="text-gray-500 font-medium">Total Requests</p>
        <p class="text-2xl font-bold text-gray-800">3</p>
      </div>
      <Inbox class="w-7 h-7 text-blue-600" />
    </div>

    <!-- Sent -->
    <div class="bg-white shadow-md p-6 rounded-xl flex justify-between items-center border">
      <div>
        <p class="text-gray-500 font-medium">Sent</p>
        <p class="text-xl font-semibold text-yellow-600">1</p>
      </div>
      <Send class="w-7 h-7 text-yellow-500" />
    </div>

    <!-- Paid -->
    <div class="bg-white shadow-md p-6 rounded-xl flex justify-between items-center border">
      <div>
        <p class="text-gray-500 font-medium">Paid</p>
        <p class="text-xl font-semibold text-green-600">1</p>
      </div>
      <BadgeCheck class="w-7 h-7 text-green-500" />
    </div>

    <!-- Total Amount -->
    <div class="bg-white shadow-md p-6 rounded-xl flex justify-between items-center border">
      <div>
        <p class="text-gray-500 font-medium">Total Amount</p>
        <p class="text-xl font-semibold text-indigo-600">₹105,000</p>
      </div>
      <IndianRupee class="w-7 h-7 text-indigo-500" />
    </div>
  </div>
</template>

<script setup>
import { Inbox, Send, BadgeCheck, IndianRupee } from 'lucide-vue-next'
</script>
