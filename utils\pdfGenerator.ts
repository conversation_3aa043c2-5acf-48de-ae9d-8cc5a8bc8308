import type { Invoice } from '~/services/invoiceApi'

export interface PDFOptions {
  filename?: string
  format?: 'A4' | 'Letter'
  orientation?: 'portrait' | 'landscape'
}

export class InvoicePDFGenerator {
  private invoice: Invoice
  private options: PDFOptions

  constructor(invoice: Invoice, options: PDFOptions = {}) {
    this.invoice = invoice
    this.options = {
      filename: `Invoice-${invoice.invoiceNumber}.pdf`,
      format: 'A4',
      orientation: 'portrait',
      ...options
    }
  }

  // Generate and download PDF as actual file
  async generateAndDownload(): Promise<void> {
    try {
      // Create a new window with PDF-optimized content
      const pdfWindow = window.open('', '_blank', 'width=800,height=600')

      if (!pdfWindow) {
        throw new Error('Unable to open PDF window. Please check your popup blocker settings.')
      }

      // Generate the HTML content with PDF-specific styling
      const htmlContent = this.generatePDFHTML()

      // Write content to the new window
      pdfWindow.document.write(htmlContent)
      pdfWindow.document.close()

      // Wait for content to load
      await new Promise(resolve => {
        pdfWindow.onload = resolve
        setTimeout(resolve, 1500) // Longer timeout for PDF generation
      })

      // Set the document title for the PDF filename
      pdfWindow.document.title = this.options.filename || `Invoice-${this.invoice.invoiceNumber}.pdf`

      // Focus the window and trigger print dialog
      pdfWindow.focus()

      // Add instructions for the user
      const instructionDiv = pdfWindow.document.createElement('div')
      instructionDiv.innerHTML = `
        <div style="position: fixed; top: 10px; right: 10px; background: #4CAF50; color: white; padding: 10px; border-radius: 5px; z-index: 1000; font-family: Arial, sans-serif; font-size: 12px;">
          <strong>To download as PDF:</strong><br>
          1. Press Ctrl+P (Cmd+P on Mac)<br>
          2. Select "Save as PDF" as destination<br>
          3. Click "Save"
        </div>
      `
      pdfWindow.document.body.appendChild(instructionDiv)

      // Auto-trigger print dialog after a short delay
      setTimeout(() => {
        pdfWindow.print()
      }, 500)

    } catch (error) {
      console.error('Error generating PDF:', error)
      throw new Error('Failed to generate PDF. Please try again.')
    }
  }

  // Generate PDF-optimized HTML with enhanced styling
  private generatePDFHTML(): string {
    const { invoice } = this

    return `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${this.options.filename || `Invoice-${invoice.invoiceNumber}.pdf`}</title>
        <style>
          ${this.getPDFStyles()}
        </style>
      </head>
      <body>
        <div class="pdf-container">
          ${this.generateHeader()}
          ${this.generateInvoiceInfo()}
          ${this.generateItemsTable()}
          ${this.generateTotals()}
          ${this.generateFooter()}
        </div>
        <script>
          // Auto-focus for better PDF generation
          window.onload = function() {
            window.focus();
            // Remove instruction after 10 seconds
            setTimeout(function() {
              const instruction = document.querySelector('[style*="position: fixed"]');
              if (instruction) instruction.remove();
            }, 10000);
          };
        </script>
      </body>
      </html>
    `
  }

  // Generate print-optimized HTML
  private generateHTML(): string {
    const { invoice } = this
    
    return `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Invoice ${invoice.invoiceNumber}</title>
        <style>
          ${this.getStyles()}
        </style>
      </head>
      <body>
        <div class="invoice-container">
          ${this.generateHeader()}
          ${this.generateInvoiceInfo()}
          ${this.generateClientInfo()}
          ${this.generateItemsTable()}
          ${this.generateTotals()}
          ${this.generateFooter()}
        </div>
      </body>
      </html>
    `
  }

  private getPDFStyles(): string {
    return `
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: 'Arial', 'Helvetica', sans-serif;
        font-size: 12px;
        line-height: 1.4;
        color: #000;
        background: white;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
      }

      .pdf-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 40px;
        background: white;
      }

      .header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 40px;
        border-bottom: 2px solid #000;
        padding-bottom: 20px;
      }

      .company-info h1 {
        font-size: 28px;
        font-weight: bold;
        color: #000;
        margin-bottom: 5px;
      }

      .company-info p {
        color: #333;
        font-size: 14px;
      }

      .invoice-title {
        text-align: right;
      }

      .invoice-title h2 {
        font-size: 36px;
        font-weight: bold;
        color: #000;
        margin-bottom: 5px;
      }

      .invoice-number {
        font-size: 16px;
        color: #333;
        font-weight: 500;
      }

      .invoice-details {
        display: flex;
        justify-content: space-between;
        margin-bottom: 40px;
      }

      .bill-to, .invoice-info {
        flex: 1;
      }

      .bill-to {
        margin-right: 40px;
      }

      .section-title {
        font-size: 14px;
        font-weight: bold;
        color: #000;
        margin-bottom: 15px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      .client-name {
        font-size: 18px;
        font-weight: bold;
        color: #000;
        margin-bottom: 8px;
      }

      .client-details p, .invoice-info p {
        margin-bottom: 5px;
        color: #333;
      }

      .status-badges {
        margin-top: 10px;
      }

      .status-badge {
        display: inline-block;
        padding: 4px 12px;
        border: 1px solid #000;
        border-radius: 3px;
        font-size: 11px;
        font-weight: bold;
        text-transform: uppercase;
        margin-right: 8px;
        background: white;
        color: #000;
      }

      .items-table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 30px;
      }

      .items-table th {
        background-color: #f0f0f0;
        padding: 15px 12px;
        text-align: left;
        font-weight: bold;
        color: #000;
        border: 1px solid #000;
        font-size: 12px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      .items-table th:last-child,
      .items-table td:last-child {
        text-align: right;
      }

      .items-table td {
        padding: 12px;
        border: 1px solid #000;
        color: #000;
      }

      .item-description {
        font-weight: 500;
        color: #000;
      }

      .item-type {
        font-size: 10px;
        color: #666;
        text-transform: capitalize;
      }

      .totals-section {
        display: flex;
        justify-content: flex-end;
        margin-bottom: 40px;
      }

      .totals-table {
        width: 300px;
      }

      .totals-row {
        display: flex;
        justify-content: space-between;
        padding: 8px 0;
        border-bottom: 1px solid #ccc;
      }

      .totals-row:last-child {
        border-bottom: 2px solid #000;
        border-top: 2px solid #000;
        font-weight: bold;
        font-size: 16px;
        color: #000;
        padding: 15px 0;
      }

      .totals-label {
        color: #000;
      }

      .totals-amount {
        font-weight: 500;
        color: #000;
      }

      .notes-section {
        margin-top: 40px;
        padding-top: 20px;
        border-top: 1px solid #000;
      }

      .notes-content {
        color: #000;
        line-height: 1.6;
        white-space: pre-wrap;
      }

      .footer {
        margin-top: 60px;
        padding-top: 20px;
        border-top: 1px solid #000;
        text-align: center;
        color: #333;
        font-size: 11px;
      }

      @media print {
        body {
          -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
        }

        .pdf-container {
          padding: 20px;
        }

        @page {
          margin: 0.5in;
          size: A4;
        }

        /* Hide the instruction div when printing */
        [style*="position: fixed"] {
          display: none !important;
        }
      }

      @media screen {
        .pdf-container {
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
          border: 1px solid #e5e7eb;
        }
      }
    `
  }

  private getStyles(): string {
    return `
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: 'Arial', 'Helvetica', sans-serif;
        font-size: 12px;
        line-height: 1.4;
        color: #333;
        background: white;
      }

      .invoice-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 40px;
        background: white;
      }

      .header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 40px;
        border-bottom: 2px solid #e5e7eb;
        padding-bottom: 20px;
      }

      .company-info h1 {
        font-size: 28px;
        font-weight: bold;
        color: #1f2937;
        margin-bottom: 5px;
      }

      .company-info p {
        color: #6b7280;
        font-size: 14px;
      }

      .invoice-title {
        text-align: right;
      }

      .invoice-title h2 {
        font-size: 36px;
        font-weight: bold;
        color: #059669;
        margin-bottom: 5px;
      }

      .invoice-number {
        font-size: 16px;
        color: #6b7280;
        font-weight: 500;
      }

      .invoice-details {
        display: flex;
        justify-content: space-between;
        margin-bottom: 40px;
      }

      .bill-to, .invoice-info {
        flex: 1;
      }

      .bill-to {
        margin-right: 40px;
      }

      .section-title {
        font-size: 14px;
        font-weight: bold;
        color: #374151;
        margin-bottom: 15px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      .client-name {
        font-size: 18px;
        font-weight: bold;
        color: #1f2937;
        margin-bottom: 8px;
      }

      .client-details p, .invoice-info p {
        margin-bottom: 5px;
        color: #4b5563;
      }

      .status-badges {
        margin-top: 10px;
      }

      .status-badge {
        display: inline-block;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 11px;
        font-weight: bold;
        text-transform: uppercase;
        margin-right: 8px;
      }

      .status-paid {
        background-color: #d1fae5;
        color: #065f46;
      }

      .status-overdue {
        background-color: #fee2e2;
        color: #991b1b;
      }

      .status-sent {
        background-color: #dbeafe;
        color: #1e40af;
      }

      .items-table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 30px;
      }

      .items-table th {
        background-color: #f9fafb;
        padding: 15px 12px;
        text-align: left;
        font-weight: bold;
        color: #374151;
        border-bottom: 2px solid #e5e7eb;
        font-size: 12px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      .items-table th:last-child,
      .items-table td:last-child {
        text-align: right;
      }

      .items-table td {
        padding: 12px;
        border-bottom: 1px solid #f3f4f6;
        color: #4b5563;
      }

      .item-description {
        font-weight: 500;
        color: #1f2937;
      }

      .item-type {
        font-size: 10px;
        color: #9ca3af;
        text-transform: capitalize;
      }

      .totals-section {
        display: flex;
        justify-content: flex-end;
        margin-bottom: 40px;
      }

      .totals-table {
        width: 300px;
      }

      .totals-row {
        display: flex;
        justify-content: space-between;
        padding: 8px 0;
        border-bottom: 1px solid #f3f4f6;
      }

      .totals-row:last-child {
        border-bottom: 2px solid #e5e7eb;
        border-top: 2px solid #e5e7eb;
        font-weight: bold;
        font-size: 16px;
        color: #1f2937;
        padding: 15px 0;
      }

      .totals-label {
        color: #6b7280;
      }

      .totals-amount {
        font-weight: 500;
        color: #1f2937;
      }

      .notes-section {
        margin-top: 40px;
        padding-top: 20px;
        border-top: 1px solid #e5e7eb;
      }

      .notes-content {
        color: #4b5563;
        line-height: 1.6;
        white-space: pre-wrap;
      }

      .footer {
        margin-top: 60px;
        padding-top: 20px;
        border-top: 1px solid #e5e7eb;
        text-align: center;
        color: #9ca3af;
        font-size: 11px;
      }

      @media print {
        body {
          -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
        }
        
        .invoice-container {
          padding: 20px;
        }
        
        @page {
          margin: 0.5in;
          size: A4;
        }
      }
    `
  }

  private generateHeader(): string {
    return `
      <div class="header">
        <div class="company-info">
          <h1>Invoice @Easy</h1>
          <p>Professional Invoice Management</p>
        </div>
        <div class="invoice-title">
          <h2>INVOICE</h2>
          <div class="invoice-number">${this.invoice.invoiceNumber}</div>
        </div>
      </div>
    `
  }

  private generateInvoiceInfo(): string {
    const issueDate = this.formatDate(this.invoice.issueDate)
    const dueDate = this.formatDate(this.invoice.dueDate)
    const isOverdue = this.isOverdue()
    
    return `
      <div class="invoice-details">
        <div class="bill-to">
          <div class="section-title">Bill To</div>
          <div class="client-name">${this.invoice.clientName}</div>
          <div class="client-details">
            <p>${this.invoice.clientEmail}</p>
            ${this.invoice.clientPhone ? `<p>${this.invoice.clientPhone}</p>` : ''}
            ${this.generateClientAddress()}
          </div>
        </div>
        <div class="invoice-info">
          <div class="section-title">Invoice Details</div>
          <p><strong>Issue Date:</strong> ${issueDate}</p>
          <p><strong>Due Date:</strong> ${dueDate}</p>
          <p><strong>Type:</strong> ${this.invoice.invoiceType.charAt(0).toUpperCase() + this.invoice.invoiceType.slice(1)}</p>
          ${this.invoice.paymentTerms ? `<p><strong>Payment Terms:</strong> ${this.invoice.paymentTerms}</p>` : ''}
          <div class="status-badges">
            ${this.invoice.isPaid ? '<span class="status-badge status-paid">Paid</span>' : ''}
            ${isOverdue ? '<span class="status-badge status-overdue">Overdue</span>' : ''}
            ${!this.invoice.isPaid && !isOverdue ? '<span class="status-badge status-sent">Sent</span>' : ''}
          </div>
        </div>
      </div>
    `
  }

  private generateClientInfo(): string {
    return '' // Already included in generateInvoiceInfo
  }

  private generateClientAddress(): string {
    const address = this.invoice.clientAddress
    if (!address) return ''

    const parts = []
    if (address.street) parts.push(`<p>${address.street}</p>`)
    
    const cityStateZip = [address.city, address.state, address.zipCode].filter(Boolean).join(', ')
    if (cityStateZip) parts.push(`<p>${cityStateZip}</p>`)
    
    if (address.country) parts.push(`<p>${address.country}</p>`)
    
    return parts.join('')
  }

  private generateItemsTable(): string {
    const items = this.invoice.items.map(item => `
      <tr>
        <td>
          <div class="item-description">${item.description}</div>
          ${item.type ? `<div class="item-type">${item.type}</div>` : ''}
        </td>
        <td style="text-align: center;">${item.quantity}</td>
        <td style="text-align: center;">₹${this.formatCurrency(item.unitPrice)}</td>
        <td>₹${this.formatCurrency(item.total || (item.quantity * item.unitPrice))}</td>
      </tr>
    `).join('')

    return `
      <table class="items-table">
        <thead>
          <tr>
            <th>Description</th>
            <th style="text-align: center;">Quantity</th>
            <th style="text-align: center;">Unit Price</th>
            <th>Total</th>
          </tr>
        </thead>
        <tbody>
          ${items}
        </tbody>
      </table>
    `
  }

  private generateTotals(): string {
    const { invoice } = this
    
    return `
      <div class="totals-section">
        <div class="totals-table">
          <div class="totals-row">
            <span class="totals-label">Subtotal:</span>
            <span class="totals-amount">₹${this.formatCurrency(invoice.subtotal)}</span>
          </div>
          ${invoice.discountRate > 0 ? `
            <div class="totals-row">
              <span class="totals-label">Discount (${invoice.discountRate}%):</span>
              <span class="totals-amount">-₹${this.formatCurrency(invoice.discountAmount)}</span>
            </div>
          ` : ''}
          ${invoice.taxRate > 0 ? `
            <div class="totals-row">
              <span class="totals-label">Tax (${invoice.taxRate}%):</span>
              <span class="totals-amount">₹${this.formatCurrency(invoice.taxAmount)}</span>
            </div>
          ` : ''}
          <div class="totals-row">
            <span class="totals-label">Total:</span>
            <span class="totals-amount">₹${this.formatCurrency(invoice.total)}</span>
          </div>
        </div>
      </div>
    `
  }

  private generateFooter(): string {
    const notes = this.invoice.notes ? `
      <div class="notes-section">
        <div class="section-title">Notes</div>
        <div class="notes-content">${this.invoice.notes}</div>
      </div>
    ` : ''

    return `
      ${notes}
      <div class="footer">
        <p>Generated on ${this.formatDate(new Date().toISOString())} | Invoice @Easy - Professional Invoice Management</p>
      </div>
    `
  }

  private formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  private formatCurrency(amount: number): string {
    return amount.toFixed(2)
  }

  private isOverdue(): boolean {
    if (this.invoice.isPaid) return false
    const currentDate = new Date()
    const dueDate = new Date(this.invoice.dueDate)
    return currentDate > dueDate
  }
}

// Utility function for easy use
export const generateInvoicePDF = async (invoice: Invoice, options?: PDFOptions): Promise<void> => {
  const generator = new InvoicePDFGenerator(invoice, options)
  await generator.generateAndDownload()
}

export default InvoicePDFGenerator
