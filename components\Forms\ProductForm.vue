<template>
  <div class="bg-white rounded-lg shadow-sm border p-6">
    <div class="flex justify-between items-center mb-6">
      <h2 class="text-xl font-semibold text-gray-900">
        {{ isEditing ? 'Edit Product' : 'Create New Product' }}
      </h2>
      <button
        @click="$emit('close')"
        class="text-gray-400 hover:text-gray-600"
      >
        <Icon name="lucide:x" class="w-6 h-6" />
      </button>
    </div>

    <form @submit.prevent="handleSubmit" class="space-y-6">
      <!-- Basic Information -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Product ID
          </label>
          <input
            v-model="form.productId"
            type="text"
            class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50"
            placeholder="Auto-generated if empty"
            readonly
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Product Name *
          </label>
          <input
            v-model="form.name"
            type="text"
            required
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#00C951] focus:border-transparent"
            placeholder="Product name"
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Category *
          </label>
          <select
            v-model="form.category"
            required
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#00C951] focus:border-transparent"
          >
            <option value="">Select category</option>
            <option
              v-for="category in productCategories"
              :key="category"
              :value="category"
            >
              {{ category }}
            </option>
          </select>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Unit *
          </label>
          <select
            v-model="form.unit"
            required
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#00C951] focus:border-transparent"
          >
            <option value="">Select unit</option>
            <option value="piece">Piece</option>
            <option value="kg">Kilogram</option>
            <option value="liter">Liter</option>
            <option value="meter">Meter</option>
            <option value="box">Box</option>
            <option value="pack">Pack</option>
            <option value="set">Set</option>
            <option value="hour">Hour</option>
            <option value="other">Other</option>
          </select>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Cost Price *
          </label>
          <input
            v-model.number="form.costPrice"
            type="number"
            step="0.01"
            min="0"
            required
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#00C951] focus:border-transparent"
            placeholder="0.00"
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Selling Price *
          </label>
          <input
            v-model.number="form.sellingPrice"
            type="number"
            step="0.01"
            min="0"
            required
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#00C951] focus:border-transparent"
            placeholder="0.00"
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Tax Rate (%)
          </label>
          <input
            v-model.number="form.taxRate"
            type="number"
            min="0"
            max="100"
            step="0.01"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#00C951] focus:border-transparent"
            placeholder="0"
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Status
          </label>
          <select
            v-model="form.status"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#00C951] focus:border-transparent"
          >
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
          </select>
        </div>
      </div>

      <!-- Inventory Information -->
      <div class="border-t pt-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Inventory Information</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              Current Stock *
            </label>
            <input
              v-model.number="form.stock"
              type="number"
              min="0"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#00C951] focus:border-transparent"
              placeholder="0"
            />
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              Minimum Stock Level
            </label>
            <input
              v-model.number="form.minStockLevel"
              type="number"
              min="0"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#00C951] focus:border-transparent"
              placeholder="0"
            />
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              Maximum Stock Level
            </label>
            <input
              v-model.number="form.maxStockLevel"
              type="number"
              min="0"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#00C951] focus:border-transparent"
              placeholder="0"
            />
          </div>
        </div>
      </div>

      <!-- Description -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">
          Description
        </label>
        <textarea
          v-model="form.description"
          rows="4"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#00C951] focus:border-transparent"
          placeholder="Product description..."
        ></textarea>
      </div>

      <!-- Profit Margin Display -->
      <div class="bg-gray-50 p-4 rounded-md">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <span class="text-sm text-gray-600">Cost Price:</span>
            <div class="font-medium">${{ form.costPrice.toFixed(2) }}</div>
          </div>
          <div>
            <span class="text-sm text-gray-600">Selling Price:</span>
            <div class="font-medium">${{ form.sellingPrice.toFixed(2) }}</div>
          </div>
          <div>
            <span class="text-sm text-gray-600">Profit Margin:</span>
            <div class="font-bold" :class="profitMargin >= 0 ? 'text-green-600' : 'text-red-600'">
              {{ profitMargin.toFixed(2) }}%
            </div>
          </div>
        </div>
      </div>

      <!-- Form Actions -->
      <div class="flex justify-end space-x-4 pt-6 border-t">
        <button
          type="button"
          @click="$emit('close')"
          class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
        >
          Cancel
        </button>
        <button
          type="submit"
          :disabled="isSubmitting"
          class="px-6 py-2 bg-[#00C951] text-white rounded-md hover:bg-[#00B847] transition-colors disabled:opacity-50"
        >
          {{ isSubmitting ? 'Saving...' : (isEditing ? 'Update Product' : 'Create Product') }}
        </button>
      </div>
    </form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useProductsStore } from '~/stores/products'
import type { Product } from '~/services/productApi'

interface Props {
  product?: Product | null
}

const props = defineProps<Props>()
const emit = defineEmits<{
  close: []
  success: [product: Product]
}>()

const productsStore = useProductsStore()

const isSubmitting = ref(false)
const isEditing = computed(() => !!props.product)

// Product categories
const productCategories = [
  'Electronics',
  'Clothing',
  'Books',
  'Home & Garden',
  'Sports',
  'Toys',
  'Health & Beauty',
  'Automotive',
  'Office Supplies',
  'Software',
  'Digital Products',
  'Other'
]

// Form data
const form = reactive({
  productId: '',
  name: '',
  category: '',
  unit: '',
  costPrice: 0,
  sellingPrice: 0,
  taxRate: 0,
  status: 'active' as 'active' | 'inactive',
  stock: 0,
  minStockLevel: 0,
  maxStockLevel: 0,
  description: ''
})

// Computed profit margin
const profitMargin = computed(() => {
  if (form.costPrice === 0) return 0
  return ((form.sellingPrice - form.costPrice) / form.costPrice) * 100
})

// Load data on mount
onMounted(async () => {
  try {
    // If editing, populate form with product data
    if (props.product) {
      populateForm(props.product)
    }
  } catch (error) {
    console.error('Failed to load product data:', error)
  }
})

// Populate form with product data for editing
const populateForm = (product: Product) => {
  form.productId = product.productId
  form.name = product.name
  form.category = product.category
  form.unit = product.unit
  form.costPrice = product.costPrice
  form.sellingPrice = product.sellingPrice
  form.taxRate = product.taxRate
  form.status = product.status
  form.stock = product.stock
  form.minStockLevel = product.minStockLevel
  form.maxStockLevel = product.maxStockLevel
  form.description = product.description || ''
}

// Handle form submission
const handleSubmit = async () => {
  try {
    isSubmitting.value = true
    
    const productData = { ...form }
    
    let result
    if (isEditing.value && props.product) {
      result = await productsStore.updateProduct(props.product._id, productData)
    } else {
      result = await productsStore.createProduct(productData)
    }
    
    emit('success', result)
    emit('close')
  } catch (error) {
    console.error('Failed to save product:', error)
  } finally {
    isSubmitting.value = false
  }
}
</script>
