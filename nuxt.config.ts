// https://nuxt.com/docs/api/configuration/nuxt-config
import tailwindcss from '@tailwindcss/vite'
export default defineNuxtConfig({
  compatibilityDate: '2024-11-01',
  devtools: { enabled: true },
  runtimeConfig: {
    public: {
      apiBaseUrl: process.env.API_BASE_URL || 'http://localhost:5000/api',
      siteUrl: process.env.NUXT_PUBLIC_SITE_URL || 'http://localhost:3000',
      supabase: {
        url: process.env.SUPABASE_URL,
        anonKey: process.env.SUPABASE_ANON_KEY
      }
    }
  },
  modules: [
    '@nuxt/eslint',
    '@nuxt/image',
    '@nuxtjs/supabase',
    '@nuxt/ui',
    '@nuxt/test-utils',
    '@nuxt/icon',
    '@nuxt/scripts',
    '@nuxt/content',
    '@nuxt/fonts',
    'shadcn-nuxt',
    'nuxt-lucide-icons',
    '@nuxt-alt/http',
    '@pinia/nuxt',
  ],
  components: [
  {
    path: '~/components',
    extensions: ['.vue'],
    pathPrefix: false
  }
],
  supabase: {
    redirectOptions: {
      login: '/Auth/login',
      callback: '/', // after login success
      exclude: [
        '/Auth/register',
        '/Auth/home',
        '/Auth/forgot-password',
        '/Auth/reset-password',
        '/Auth/verify-email',
        '/Auth/verify-otp'
      ],
      include: undefined, // exclude handles public routes, everything else is protected
      saveRedirectToCookie: false // not needed unless using OAuth/magic links
    },
    // Add CORS configuration
    clientOptions: {
      auth: {
        flowType: 'pkce',
        detectSessionInUrl: true,
        persistSession: true,
        autoRefreshToken: true
      }
    }
  },
  shadcn: {
    /**
     * Prefix for all the imported component
     */
    prefix: '',
    /**
     * Directory that the component lives in.
     * @default "./components/ui"
     */
    componentDir: './components/ui'
  },
 app: {
  head: {
    title: 'Invoice @Easy',
    link: [
      {
        rel: "stylesheet",
        href: "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css"
      },
      {
        rel: "stylesheet",
        href: "https://fonts.googleapis.com/css2?family=Roboto+Mono:ital,wght@0,100..700;1,100..700&display=swap"
       },
      {
        rel: "stylesheet",
        href: "https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css"
      },
    ],
  }
 },

 css: [
  '~/assets/css/tailwind.css',
],
  vite: {
    plugins: [
      tailwindcss(),
    ],
    server: {
      cors: true,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET,HEAD,PUT,PATCH,POST,DELETE',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization'
      }
    }
  },

  // Development server configuration
  devServer: {
    port: 3000,
    host: 'localhost'
  },
  plugins: [
    // Pinia plugin (for state management)

    // Toast plugin (for notifications)
    { src: '~/plugins/toast.client.ts', mode: 'client' },
  ],
})