import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { userApiService } from '~/services/userApi'
import type { UserProfile, UserSettings, UpdateProfileData, UpdatePasswordData } from '~/services/userApi'

export const useUserStore = defineStore('user', () => {
  // State
  const profile = ref<UserProfile | null>(null)
  const settings = ref<UserSettings | null>(null)
  const isLoading = ref(false)
  const isUpdating = ref(false)
  const error = ref<string | null>(null)

  // Computed
  const hasProfile = computed(() => !!profile.value)
  const hasSettings = computed(() => !!settings.value)
  const userDisplayName = computed(() => {
    if (!profile.value) return ''
    return profile.value.full_name || profile.value.email || 'User'
  })

  // Actions
  const fetchProfile = async () => {
    try {
      isLoading.value = true
      error.value = null
      
      const profileData = await userApiService.getUserProfile()
      profile.value = profileData
      
      return profileData
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch profile'
      error.value = errorMessage
      console.error('Error fetching profile:', err)
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const updateProfile = async (profileData: UpdateProfileData) => {
    try {
      isUpdating.value = true
      error.value = null
      
      const updatedProfile = await userApiService.updateUserProfile(profileData)
      profile.value = updatedProfile
      
      return updatedProfile
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update profile'
      error.value = errorMessage
      console.error('Error updating profile:', err)
      throw err
    } finally {
      isUpdating.value = false
    }
  }

  const updatePassword = async (passwordData: UpdatePasswordData) => {
    try {
      isUpdating.value = true
      error.value = null
      
      await userApiService.updatePassword(passwordData)
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update password'
      error.value = errorMessage
      console.error('Error updating password:', err)
      throw err
    } finally {
      isUpdating.value = false
    }
  }

  const fetchSettings = async () => {
    try {
      isLoading.value = true
      error.value = null
      
      const settingsData = await userApiService.getUserSettings()
      settings.value = settingsData
      
      return settingsData
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch settings'
      error.value = errorMessage
      console.error('Error fetching settings:', err)
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const updateSettings = async (settingsData: Partial<UserSettings>) => {
    try {
      isUpdating.value = true
      error.value = null
      
      const updatedSettings = await userApiService.updateUserSettings(settingsData)
      settings.value = updatedSettings
      
      return updatedSettings
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update settings'
      error.value = errorMessage
      console.error('Error updating settings:', err)
      throw err
    } finally {
      isUpdating.value = false
    }
  }

  const clearError = () => {
    error.value = null
  }

  const resetStore = () => {
    profile.value = null
    settings.value = null
    isLoading.value = false
    isUpdating.value = false
    error.value = null
  }

  // Initialize profile and settings
  const initialize = async () => {
    try {
      await Promise.all([
        fetchProfile(),
        fetchSettings()
      ])
    } catch (err) {
      console.error('Error initializing user store:', err)
      // Don't throw here, let individual components handle errors
    }
  }

  return {
    // State
    profile,
    settings,
    isLoading,
    isUpdating,
    error,

    // Computed
    hasProfile,
    hasSettings,
    userDisplayName,

    // Actions
    fetchProfile,
    updateProfile,
    updatePassword,
    fetchSettings,
    updateSettings,
    clearError,
    resetStore,
    initialize
  }
})
