<script setup>
import { ref } from 'vue'
import { FileText } from 'lucide-vue-next'

const selectedTemplateKey = ref(null)
const showModal = ref(false)

const openPreview = (key) => {
  selectedTemplateKey.value = key
  showModal.value = true
}

const templates = ref([
  {
    name: 'A4 Professional',
    description: 'Clean, professional A4 format',
    type: 'Free',
    key: 'a4-professional',
    selected: true,
  },
  {
    name: 'Thermal Printer',
    description: 'Optimized for thermal printers',
    type: 'Free',
    key: 'thermal-printer',
    selected: false,
  },
  {
    name: 'Mobile Receipt',
    description: 'Mobile-optimized receipt format',
    type: 'Premium',
    key: 'mobile-receipt',
    selected: false,
  },
])
</script>

<template>
  <!-- Template Cards UI Same as Before -->
 <div class="py-5 p-6">
  <div class="bg-white shadow rounded-2xl border p-8">
   <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
    <div
      v-for="template in templates"
      :key="template.key"
      class="rounded-2xl border p-4 h-[350px] flex flex-col justify-between shadow-xl transition hover:shadow-lg hover:border-green-400"
    >
      <div class="flex-1 flex items-center justify-center bg-muted/30 rounded-xl h-48 mb-4">
        <FileText class="w-10 h-10 text-muted-foreground" />
      </div>
      <div>
        <h3 class="text-lg font-semibold">{{ template.name }}</h3>
        <p class="text-muted-foreground text-sm">{{ template.description }}</p>
      </div>
      <div class="mt-4 flex items-center justify-between">
        <span
          class="text-xs font-medium px-2 py-1 rounded-full"
          :class="template.type === 'Free'
            ? 'bg-green-100 text-green-600'
            : 'bg-yellow-100 text-yellow-700'"
        >
          {{ template.type }}
        </span>
        <button
          class="text-sm px-3 py-1 rounded-lg bg-primary text-white hover:bg-primary/90"
          @click="openPreview(template.key)"
        >
          Preview
        </button>
      </div>
    </div>
  </div>
 </div>
 </div>

  <TemplatePreviewModal
    :show="showModal"
    :template-key="selectedTemplateKey"
    @close="showModal = false"
  />
</template>
