<script setup lang="ts">
import { <PERSON><PERSON>, <PERSON>, <PERSON>Che<PERSON>, File } from 'lucide-vue-next'

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>rigger,
} from '@/components/ui/tabs'

import { ref, onMounted } from 'vue'
import { useClientStore } from '~/stores/clients'
import { useToast } from 'vue-toastification'
import { ClientTotal } from '#components'

definePageMeta({
  middleware: 'auth'
})

const clientStore = useClientStore()
const toast = useToast()
const showClientModal = ref(false)
const selectedClient = ref(null)

function handleHeadingClick(action: string) {
  if (action === 'openClient') {
    selectedClient.value = null // Clear for new client
    showClientModal.value = true
  }
}

async function onClientSaved() {
  showClientModal.value = false
  selectedClient.value = null

  // Clear any search filters to show all clients
  clientStore.resetFilters()

  // Refresh the clients list and stats
  await clientStore.refreshClients()
}

function closeModal() {
  showClientModal.value = false
  selectedClient.value = null
}

async function onClientUpdated() {
  await clientStore.refreshClients()
}

async function onClientDeleted() {
  await clientStore.refreshClients()
}



// Load clients on component mount
onMounted(async () => {
  try {
    await clientStore.refreshClients()
  } catch (error: any) {
    toast.error('Failed to load clients: ' + error.message)
  }
})
</script>

<template>
    <Heading
      title="Manage Clients"
      description="Manage your clients and their details"
      icon="lucide:users"
      iconColor="text-green-400"
      bgColor="bg-white"
      :buttons="[
        {
          label: 'Add Client',
          icon: 'lucide:circle-plus',
          bgColor: 'bg-[#00C951]',
          textColor: 'text-white',
          action: 'openClient'
        }
      ]"
      @button-click="handleHeadingClick"
    />

  <div class="py-5 px-4 relative z-0">
    <Tabs default-value="Home" class="w-full bg-white relative z-10">
      <TabsList
        class="flex flex-wrap gap-5 w-full bg-white z-20"
      >
        <TabsTrigger
          value="Home"
          class="flex items-center gap-2 px-4 py-2 rounded-md"
        >
          <User class="w-4 h-4" /> Home
        </TabsTrigger>
        <TabsTrigger
          value="Allclients"
          class="flex items-center gap-2 px-4 py-2 rounded-md"
        >
          <Bell class="w-4 h-4" /> View All Clients
        </TabsTrigger>
        <TabsTrigger
          value="Import"
          class="flex items-center gap-2 px-4 py-2 rounded-md"
        >
          <ShieldCheck class="w-4 h-4" /> Import
        </TabsTrigger>
        <TabsTrigger
          value="Reports"
          class="flex items-center gap-2 px-4 py-2 rounded-md"
        >
          <File class="w-4 h-4" /> Reports
        </TabsTrigger>
      </TabsList>

      <TabsContent value="Home" class="relative z-10 mt-6">
        <ClientTotal/>
       <Customertransaction
      :clients="clientStore.clients"
      @client-updated="onClientUpdated"
      @client-deleted="onClientDeleted"
      />
      </TabsContent>
      <TabsContent value="Allclients" class="relative z-10 mt-6">
        <TotalClients class="" />
      </TabsContent>
      <TabsContent value="Import" class="relative z-10 mt-6">
        <ImportUploader class="py-20" />
      </TabsContent>
      <TabsContent value="Reports" class="relative z-10 mt-6">
        <Reports />
      </TabsContent>
    </Tabs>
    <ClientModal
      :open="showClientModal"
      :client="selectedClient"
      @close="closeModal"
      @client-saved="onClientSaved"
    />
  </div>
</template>
