import { defineStore } from 'pinia'
import { serviceApi, type Service, type ServiceStats, type ServiceParams } from '~/services/serviceApi'

interface ServicesState {
  services: Service[]
  currentService: Service | null
  stats: ServiceStats | null
  categories: string[]
  loading: boolean
  error: string | null
  pagination: {
    page: number
    limit: number
    totalPages: number
    totalServices: number
    hasNextPage: boolean
    hasPrevPage: boolean
  } | null
}

export const useServicesStore = defineStore('services', {
  state: (): ServicesState => ({
    services: [],
    currentService: null,
    stats: null,
    categories: [],
    loading: false,
    error: null,
    pagination: null
  }),

  getters: {
    // Get services by status
    getServicesByStatus: (state) => (status: string) => {
      if (status === 'all') return state.services
      return state.services.filter(service => service.status === status)
    },

    // Get services by category
    getServicesByCategory: (state) => (category: string) => {
      if (category === 'all') return state.services
      return state.services.filter(service => service.category === category)
    },

    // Get services by pricing type
    getServicesByPricingType: (state) => (pricingType: string) => {
      if (pricingType === 'all') return state.services
      return state.services.filter(service => service.pricingType === pricingType)
    },

    // Get service by ID
    getServiceById: (state) => (id: string) => {
      return state.services.find(service => service._id === id)
    },

    // Get available services
    availableServices: (state) => {
      return state.services.filter(service => service.isAvailable && service.status === 'active')
    },

    // Get active services
    activeServices: (state) => {
      return state.services.filter(service => service.status === 'active')
    },

    // Calculate total service revenue
    totalServiceRevenue: (state) => {
      return state.services.reduce((total, service) => total + service.totalRevenue, 0)
    },

    // Get services count by status
    servicesCountByStatus: (state) => {
      const counts = {
        active: 0,
        inactive: 0,
        discontinued: 0
      }
      
      state.services.forEach(service => {
        if (counts.hasOwnProperty(service.status)) {
          counts[service.status as keyof typeof counts]++
        }
      })
      
      return counts
    },

    // Get services count by pricing type
    servicesCountByPricingType: (state) => {
      const counts = {
        fixed: 0,
        hourly: 0,
        daily: 0,
        monthly: 0,
        project: 0
      }
      
      state.services.forEach(service => {
        if (counts.hasOwnProperty(service.pricingType)) {
          counts[service.pricingType as keyof typeof counts]++
        }
      })
      
      return counts
    },

    // Check if there are any services
    hasServices: (state) => state.services.length > 0,

    // Check if loading
    isLoading: (state) => state.loading,

    // Check if there's an error
    hasError: (state) => !!state.error
  },

  actions: {
    // Set loading state
    setLoading(loading: boolean) {
      this.loading = loading
    },

    // Set error state
    setError(error: string | null) {
      this.error = error
    },

    // Clear error
    clearError() {
      this.error = null
    },

    // Fetch all services
    async fetchServices(params?: ServiceParams) {
      try {
        this.setLoading(true)
        this.clearError()

        const response = await serviceApi.getServices(params)
        
        this.services = response.data
        this.pagination = response.pagination

        console.log('✅ Services fetched successfully:', response.data.length)
      } catch (error) {
        console.error('❌ Error fetching services:', error)
        this.setError(error instanceof Error ? error.message : 'Failed to fetch services')
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // Fetch single service
    async fetchService(id: string) {
      try {
        this.setLoading(true)
        this.clearError()

        const response = await serviceApi.getService(id)
        
        this.currentService = response.data

        // Update the service in the list if it exists
        const index = this.services.findIndex(service => service._id === id)
        if (index !== -1) {
          this.services[index] = response.data
        }

        console.log('✅ Service fetched successfully:', response.data.name)
        return response.data
      } catch (error) {
        console.error('❌ Error fetching service:', error)
        this.setError(error instanceof Error ? error.message : 'Failed to fetch service')
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // Create new service
    async createService(serviceData: Omit<Service, '_id' | 'userId' | 'serviceId' | 'totalOrders' | 'totalRevenue' | 'averageRating' | 'totalReviews' | 'currentActiveOrders' | 'createdAt' | 'updatedAt'>) {
      try {
        this.setLoading(true)
        this.clearError()

        const response = await serviceApi.createService(serviceData)
        
        // Add the new service to the beginning of the list
        this.services.unshift(response.data)
        this.currentService = response.data

        console.log('✅ Service created successfully:', response.data.name)
        return response.data
      } catch (error) {
        console.error('❌ Error creating service:', error)
        this.setError(error instanceof Error ? error.message : 'Failed to create service')
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // Update service
    async updateService(id: string, serviceData: Partial<Service>) {
      try {
        this.setLoading(true)
        this.clearError()

        const response = await serviceApi.updateService(id, serviceData)
        
        // Update the service in the list
        const index = this.services.findIndex(service => service._id === id)
        if (index !== -1) {
          this.services[index] = response.data
        }

        // Update current service if it's the same
        if (this.currentService?._id === id) {
          this.currentService = response.data
        }

        console.log('✅ Service updated successfully:', response.data.name)
        return response.data
      } catch (error) {
        console.error('❌ Error updating service:', error)
        this.setError(error instanceof Error ? error.message : 'Failed to update service')
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // Delete service
    async deleteService(id: string) {
      try {
        this.setLoading(true)
        this.clearError()

        await serviceApi.deleteService(id)
        
        // Remove the service from the list
        this.services = this.services.filter(service => service._id !== id)

        // Clear current service if it's the deleted one
        if (this.currentService?._id === id) {
          this.currentService = null
        }

        console.log('✅ Service deleted successfully')
      } catch (error) {
        console.error('❌ Error deleting service:', error)
        this.setError(error instanceof Error ? error.message : 'Failed to delete service')
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // Fetch service statistics
    async fetchServiceStats() {
      try {
        this.clearError()

        const response = await serviceApi.getServiceStats()
        
        this.stats = response.data

        console.log('✅ Service stats fetched successfully')
        return response.data
      } catch (error) {
        console.error('❌ Error fetching service stats:', error)
        this.setError(error instanceof Error ? error.message : 'Failed to fetch service statistics')
        throw error
      }
    },

    // Fetch service categories
    async fetchServiceCategories() {
      try {
        this.clearError()

        const response = await serviceApi.getServiceCategories()
        
        this.categories = response.data

        console.log('✅ Service categories fetched successfully')
        return response.data
      } catch (error) {
        console.error('❌ Error fetching service categories:', error)
        this.setError(error instanceof Error ? error.message : 'Failed to fetch service categories')
        throw error
      }
    },

    // Toggle service availability
    async toggleServiceAvailability(id: string) {
      try {
        this.setLoading(true)
        this.clearError()

        const response = await serviceApi.toggleServiceAvailability(id)
        
        // Update the service in the list
        const index = this.services.findIndex(service => service._id === id)
        if (index !== -1) {
          this.services[index] = response.data
        }

        // Update current service if it's the same
        if (this.currentService?._id === id) {
          this.currentService = response.data
        }

        console.log('✅ Service availability toggled successfully')
        return response.data
      } catch (error) {
        console.error('❌ Error toggling service availability:', error)
        this.setError(error instanceof Error ? error.message : 'Failed to toggle service availability')
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // Set current service
    setCurrentService(service: Service | null) {
      this.currentService = service
    },

    // Clear all data
    clearData() {
      this.services = []
      this.currentService = null
      this.stats = null
      this.categories = []
      this.pagination = null
      this.error = null
    },

    // Refresh services (re-fetch with current params)
    async refreshServices() {
      await this.fetchServices()
    }
  }
})
