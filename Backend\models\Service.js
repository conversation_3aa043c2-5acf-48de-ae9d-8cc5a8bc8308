const mongoose = require('mongoose');

const serviceSchema = new mongoose.Schema({
  userId: {
    type: String,
    required: [true, 'User ID is required'],
    index: true
  },
  serviceId: {
    type: String,
    unique: true,
    index: true
  },
  name: {
    type: String,
    required: [true, 'Service name is required'],
    trim: true,
    maxlength: [100, 'Service name cannot exceed 100 characters']
  },
  description: {
    type: String,
    trim: true,
    maxlength: [1000, 'Description cannot exceed 1000 characters']
  },
  category: {
    type: String,
    required: [true, 'Category is required'],
    enum: [
      'Consulting',
      'Design',
      'Development',
      'Marketing',
      'Legal',
      'Accounting',
      'Maintenance',
      'Support',
      'Training',
      'Photography',
      'Writing',
      'Translation',
      'Other'
    ],
    default: 'Other'
  },
  // Pricing structure
  pricingType: {
    type: String,
    enum: ['fixed', 'hourly', 'daily', 'monthly', 'project'],
    required: [true, 'Pricing type is required'],
    default: 'fixed'
  },
  price: {
    type: Number,
    required: [true, 'Price is required'],
    min: [0, 'Price cannot be negative']
  },
  currency: {
    type: String,
    default: 'USD',
    enum: ['USD', 'EUR', 'GBP', 'CAD', 'AUD', 'INR', 'JPY'],
    uppercase: true
  },
  // Duration and availability
  estimatedDuration: {
    value: {
      type: Number,
      min: 0
    },
    unit: {
      type: String,
      enum: ['hours', 'days', 'weeks', 'months'],
      default: 'hours'
    }
  },
  isAvailable: {
    type: Boolean,
    default: true
  },
  // Tax
  taxRate: {
    type: Number,
    default: 0,
    min: [0, 'Tax rate cannot be negative'],
    max: [100, 'Tax rate cannot exceed 100%']
  },
  taxInclusive: {
    type: Boolean,
    default: false
  },
  // Status
  status: {
    type: String,
    enum: ['active', 'inactive', 'discontinued'],
    default: 'active'
  },
  // Service details
  deliverables: [{
    type: String,
    trim: true,
    maxlength: [200, 'Deliverable cannot exceed 200 characters']
  }],
  requirements: [{
    type: String,
    trim: true,
    maxlength: [200, 'Requirement cannot exceed 200 characters']
  }],
  // Images and media
  images: [{
    url: {
      type: String,
      trim: true
    },
    alt: {
      type: String,
      trim: true
    },
    isPrimary: {
      type: Boolean,
      default: false
    }
  }],
  // Additional fields
  tags: [{
    type: String,
    trim: true,
    maxlength: [30, 'Tag cannot exceed 30 characters']
  }],
  notes: {
    type: String,
    trim: true,
    maxlength: [1000, 'Notes cannot exceed 1000 characters']
  },
  // Performance tracking
  totalOrders: {
    type: Number,
    default: 0,
    min: 0
  },
  totalRevenue: {
    type: Number,
    default: 0,
    min: 0
  },
  averageRating: {
    type: Number,
    default: 0,
    min: 0,
    max: 5
  },
  totalReviews: {
    type: Number,
    default: 0,
    min: 0
  },
  lastOrderDate: {
    type: Date
  },
  // Booking settings
  allowBooking: {
    type: Boolean,
    default: true
  },
  advanceBookingDays: {
    type: Number,
    default: 7,
    min: 0
  },
  maxConcurrentOrders: {
    type: Number,
    default: 5,
    min: 1
  },
  currentActiveOrders: {
    type: Number,
    default: 0,
    min: 0
  }
}, {
  timestamps: true
});

// Pre-save middleware to generate service ID
serviceSchema.pre('save', function(next) {
  if (!this.serviceId) {
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    this.serviceId = `S-${year}${month}-${random}`;
  }
  
  // Ensure only one primary image
  const primaryImages = this.images.filter(img => img.isPrimary);
  if (primaryImages.length > 1) {
    this.images.forEach((img, index) => {
      img.isPrimary = index === 0;
    });
  }
  
  next();
});

// Virtual for availability status
serviceSchema.virtual('isFullyBooked').get(function() {
  return this.currentActiveOrders >= this.maxConcurrentOrders;
});

// Virtual for effective price (with tax if inclusive)
serviceSchema.virtual('effectivePrice').get(function() {
  if (this.taxInclusive) {
    return this.price;
  }
  return this.price + (this.price * this.taxRate / 100);
});

// Static method to find services by user
serviceSchema.statics.findByUser = function(userId, options = {}) {
  const { search, category, status, pricingType, available, sort = '-createdAt', limit = 10, skip = 0 } = options;
  
  let query = { userId };
  
  if (status && status !== 'all') {
    query.status = status;
  }
  
  if (category && category !== 'all') {
    query.category = category;
  }
  
  if (pricingType && pricingType !== 'all') {
    query.pricingType = pricingType;
  }
  
  if (available !== undefined) {
    query.isAvailable = available;
  }
  
  if (search) {
    query.$or = [
      { name: { $regex: search, $options: 'i' } },
      { description: { $regex: search, $options: 'i' } },
      { serviceId: { $regex: search, $options: 'i' } }
    ];
  }
  
  return this.find(query)
    .sort(sort)
    .limit(limit)
    .skip(skip);
};

// Static method to get service statistics
serviceSchema.statics.getStats = function(userId) {
  return this.aggregate([
    { $match: { userId } },
    {
      $group: {
        _id: null,
        totalServices: { $sum: 1 },
        activeServices: {
          $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] }
        },
        inactiveServices: {
          $sum: { $cond: [{ $eq: ['$status', 'inactive'] }, 1, 0] }
        },
        availableServices: {
          $sum: { $cond: [{ $eq: ['$isAvailable', true] }, 1, 0] }
        },
        totalOrders: { $sum: '$totalOrders' },
        totalRevenue: { $sum: '$totalRevenue' },
        averagePrice: { $avg: '$price' },
        averageRating: { $avg: '$averageRating' }
      }
    }
  ]);
};

module.exports = mongoose.model('Service', serviceSchema);
