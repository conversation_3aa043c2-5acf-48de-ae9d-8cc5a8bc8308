<template>
  <div class="min-h-screen bg-gray-50 flex flex-col items-center justify-center px-4 py-10">
    <!-- Header -->
    <div class="flex items-center gap-2 mb-6 w-full max-w-md">
      <ArrowLeft class="w-5 h-5 text-gray-600 cursor-pointer" @click="$router.back()" />
      <div class="flex items-center gap-2">
        <div class="w-7 h-7 rounded-full bg-gradient-to-br from-purple-500 to-blue-500 flex items-center justify-center text-white">
          <Smartphone class="w-4 h-4" />
        </div>
        <h2 class="text-lg font-semibold text-gray-800">Add UPI Payment</h2>
      </div>
    </div>

    <!-- Card -->
    <div class="bg-white rounded-xl shadow-md w-full max-w-md p-6">
      <h3 class="text-lg font-semibold text-gray-800 mb-4">UPI Details</h3>

      <!-- UPI ID -->
      <div class="mb-4">
        <label class="block text-sm font-medium text-gray-700 mb-1">UPI ID *</label>
        <input
          type="text"
          placeholder="yourname@paytm"
          class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:outline-none"
        />
        <p class="text-xs text-gray-400 mt-1">Enter your UPI ID (e.g., yourname@paytm, yourname@gpay)</p>
      </div>

      <!-- Nickname -->
      <div class="mb-4">
        <label class="block text-sm font-medium text-gray-700 mb-1">Nickname (Optional)</label>
        <input
          type="text"
          placeholder="My Primary UPI"
          class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:outline-none"
        />
      </div>

      <!-- Default Checkbox -->
      <div class="mb-6 flex items-center gap-2">
        <input id="default" type="checkbox" class="accent-blue-500" />
        <label for="default" class="text-sm text-gray-700">Set as default payment method</label>
      </div>

      <!-- Buttons -->
      <div class="flex justify-between items-center gap-4">
        <button
          class="w-full py-2 rounded-md border border-gray-300 text-gray-700 hover:bg-gray-100 text-sm font-medium"
        >
          Cancel
        </button>
        <button
          class="w-full py-2 rounded-md bg-gradient-to-r from-blue-500 to-purple-500 text-white font-medium text-sm hover:opacity-90 flex items-center justify-center gap-2"
        >
          <Check class="w-4 h-4" /> Add UPI
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Smartphone, ArrowLeft, Check } from 'lucide-vue-next'
</script>
