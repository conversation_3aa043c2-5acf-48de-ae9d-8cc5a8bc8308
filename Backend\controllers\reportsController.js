const Client = require('../models/Client');
const Invoice = require('../models/Invoice');

/**
 * @desc    Get comprehensive client reports data
 * @route   GET /api/reports/clients
 * @access  Private (Protected by Supabase JWT)
 */
const getClientReports = async (req, res) => {
  try {
    const userId = req.user?.id || '9799afbc-00e8-4678-8526-e171e357a093'; // Fallback for testing
    const { dateRange, clientId, reportType } = req.query;

    // Calculate date filter based on dateRange
    let dateFilter = {};
    const now = new Date();

    if (dateRange === 'This Month') {
      const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
      dateFilter = { createdAt: { $gte: startOfMonth } };
    } else if (dateRange === 'Last Month') {
      const startOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
      const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0);
      dateFilter = {
        createdAt: {
          $gte: startOfLastMonth,
          $lte: endOfLastMonth
        }
      };
    } else if (dateRange === 'This Year') {
      const startOfYear = new Date(now.getFullYear(), 0, 1);
      dateFilter = { createdAt: { $gte: startOfYear } };
    }

    // Build client filter
    let clientFilter = { userId };
    if (clientId && clientId !== 'All Clients') {
      clientFilter._id = clientId;
    }

    // Get all clients for the user (for dropdown)
    const allClients = await Client.find({ userId })
      .select('_id name')
      .sort({ name: 1 });

    // Get clients with date filter applied
    const filteredClients = await Client.find({ ...clientFilter, ...dateFilter })
      .select('name email type totalInvoices totalAmount status createdAt');

    // Calculate summary statistics
    const summary = await calculateSummaryStats(userId, dateFilter, clientId);

    // Get top clients by revenue
    const topClients = await getTopClientsByRevenue(userId, dateFilter, clientId);

    // Get detailed client report
    const detailedClients = await getDetailedClientReport(userId, dateFilter, clientId);

    const result = {
      summary,
      topClients,
      detailedClients,
      clients: allClients.map(client => ({
        _id: client._id,
        name: client.name
      }))
    };

    res.status(200).json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('Get client reports error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve client reports',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Calculate summary statistics using real invoice data
 */
const calculateSummaryStats = async (userId, dateFilter, clientId) => {
  try {
    // Build invoice filter
    let invoiceFilter = { userId, ...dateFilter };

    // If specific client selected, filter by client email
    if (clientId && clientId !== 'All Clients') {
      const client = await Client.findById(clientId);
      if (client) {
        invoiceFilter.clientEmail = client.email;
      }
    }

    // Get invoice statistics
    const invoiceStats = await Invoice.aggregate([
      { $match: invoiceFilter },
      {
        $group: {
          _id: null,
          totalRevenue: { $sum: '$total' },
          totalInvoices: { $sum: 1 }
        }
      }
    ]);

    // Get new clients count
    let clientFilter = { userId, ...dateFilter };
    if (clientId && clientId !== 'All Clients') {
      clientFilter._id = clientId;
    }

    const newClientsCount = await Client.countDocuments(clientFilter);

    const invoiceResult = invoiceStats[0] || { totalRevenue: 0, totalInvoices: 0 };
    const avgInvoiceValue = invoiceResult.totalInvoices > 0 ?
      invoiceResult.totalRevenue / invoiceResult.totalInvoices : 0;

    return {
      totalRevenue: invoiceResult.totalRevenue || 0,
      totalInvoices: invoiceResult.totalInvoices || 0,
      avgInvoiceValue: Math.round(avgInvoiceValue * 100) / 100,
      newClients: newClientsCount || 0
    };
  } catch (error) {
    console.error('Calculate summary stats error:', error);
    return { totalRevenue: 0, totalInvoices: 0, avgInvoiceValue: 0, newClients: 0 };
  }
};

/**
 * Get top clients by revenue using real invoice data
 */
const getTopClientsByRevenue = async (userId, dateFilter, clientId) => {
  try {
    // Build invoice filter
    let invoiceFilter = { userId, ...dateFilter };

    // If specific client selected, filter by client email
    if (clientId && clientId !== 'All Clients') {
      const client = await Client.findById(clientId);
      if (client) {
        invoiceFilter.clientEmail = client.email;
      }
    }

    // Aggregate invoice data by client
    const clientRevenue = await Invoice.aggregate([
      { $match: invoiceFilter },
      {
        $group: {
          _id: '$clientEmail',
          totalRevenue: { $sum: '$total' },
          invoiceCount: { $sum: 1 },
          clientName: { $first: '$clientName' }
        }
      },
      { $sort: { totalRevenue: -1 } },
      { $limit: 10 }
    ]);

    // Get client types for each client
    const result = [];
    for (const clientData of clientRevenue) {
      const client = await Client.findOne({
        userId,
        email: clientData._id
      }).select('type');

      result.push({
        name: clientData.clientName,
        type: client?.type?.toLowerCase() || 'individual',
        revenue: clientData.totalRevenue || 0,
        invoices: clientData.invoiceCount || 0
      });
    }

    return result;
  } catch (error) {
    console.error('Get top clients error:', error);
    return [];
  }
};

/**
 * Get detailed client report using real invoice data
 */
const getDetailedClientReport = async (userId, dateFilter, clientId) => {
  try {
    // Build client filter
    let clientFilter = { userId };
    if (clientId && clientId !== 'All Clients') {
      clientFilter._id = clientId;
    }

    // Get all clients
    const clients = await Client.find(clientFilter)
      .select('name email type status')
      .sort({ name: 1 });

    const result = [];

    for (const client of clients) {
      // Build invoice filter for this client
      let invoiceFilter = {
        userId,
        clientEmail: client.email,
        ...dateFilter
      };

      // Get invoice statistics for this client
      const invoiceStats = await Invoice.aggregate([
        { $match: invoiceFilter },
        {
          $group: {
            _id: null,
            totalRevenue: { $sum: '$total' },
            invoiceCount: { $sum: 1 }
          }
        }
      ]);

      const stats = invoiceStats[0] || { totalRevenue: 0, invoiceCount: 0 };
      const avgInvoice = stats.invoiceCount > 0 ?
        stats.totalRevenue / stats.invoiceCount : 0;

      // Only include clients with invoices in the date range (or all if no date filter)
      if (Object.keys(dateFilter).length === 0 || stats.invoiceCount > 0) {
        result.push({
          name: client.name,
          email: client.email,
          type: client.type || 'Individual',
          invoices: stats.invoiceCount,
          revenue: stats.totalRevenue,
          avgInvoice: Math.round(avgInvoice * 100) / 100,
          status: client.status || 'Active'
        });
      }
    }

    // Sort by revenue descending
    return result.sort((a, b) => b.revenue - a.revenue);
  } catch (error) {
    console.error('Get detailed client report error:', error);
    return [];
  }
};

module.exports = {
  getClientReports
};
