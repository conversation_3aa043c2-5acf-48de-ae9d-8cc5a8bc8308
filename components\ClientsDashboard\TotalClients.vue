<template>
  <div class="p-6 bg-white border rounded-xl shadow">

    <!-- Search and Filter -->
    <div class="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
      <div
        class="flex items-center border rounded-lg px-3 py-2 w-28 md:w-80 hover:w-56 hover:md:w-96 transition-all duration-300 ease-in-out bg-white shadow-sm focus-within:ring-2"
        style="--tw-ring-color: #05DF72"
      >
        <Icon name="lucide:search" class="w-5 h-5 text-gray-400" />
        <input
          v-model="searchQuery"
          type="text"
          placeholder="Search Clients..."
          class="ml-2 outline-none w-full bg-transparent text-sm text-gray-600"
        />
      </div>

      <DropdownMenu>
        <DropdownMenuTrigger as-child>
          <Button variant="outline">
            <Filter class="w-4 h-4 mr-2" />
            Filter
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem @click="sortBy('recent')">Recent Clients</DropdownMenuItem>
          <DropdownMenuItem @click="sortBy('oldest')">Oldest Clients</DropdownMenuItem>
          <DropdownMenuItem @click="sortBy('business')">Business Clients</DropdownMenuItem>
          <DropdownMenuItem @click="sortBy('individual')">Individual Clients</DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>

    <!-- Clients Grid -->
    <div v-if="filteredClients.length === 0" class="text-center py-12">
      <Icon name="lucide:users" class="w-16 h-16 mx-auto mb-4 text-gray-300" />
      <p class="text-xl font-semibold text-gray-700 mb-2">No clients found</p>
      <p class="text-gray-500">Try adjusting your search or add your first client</p>
    </div>

    <div v-else class="grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
      <div
        v-for="client in filteredClients"
        :key="client._id"
        class="bg-white border rounded-xl shadow-sm hover:shadow-lg transition-shadow duration-200 p-6 w-full"
      >
        <div class="flex justify-between items-start mb-4">
          <div class="flex-1">
            <h2 class="text-lg font-bold text-gray-900 mb-2">{{ client.name }}</h2>
            <div class="flex gap-2">
              <span
                :class="statusClass(client.status)"
                class="text-xs px-3 py-1 rounded-full font-semibold cursor-pointer hover:opacity-80 transition-opacity"
                @click.stop="toggleStatus(client)"
                title="Click to toggle status"
              >
                {{ client.status || 'Active' }}
              </span>
              <span class="text-xs bg-purple-100 text-purple-700 px-3 py-1 rounded-full font-semibold">
                {{ client.type || 'Individual' }}
              </span>
            </div>
          </div>
          <div class="flex gap-2">
            <button
              @click.stop="editClient(client)"
              class="p-1 rounded-md text-gray-400 hover:text-blue-600 hover:bg-blue-50 transition-colors"
              title="Edit Client"
            >
              <Icon name="lucide:edit" class="w-4 h-4" />
            </button>
            <button
              @click.stop="deleteClient(client)"
              class="p-1 rounded-md text-gray-400 hover:text-red-600 hover:bg-red-50 transition-colors"
              title="Delete Client"
            >
              <Icon name="lucide:trash" class="w-4 h-4" />
            </button>
          </div>
        </div>

        <div class="space-y-3 text-sm text-gray-600 mb-6">
          <div class="flex items-center gap-2">
            <Icon name="lucide:mail" class="w-4 h-4 text-gray-400" />
            <span class="truncate">{{ client.email }}</span>
          </div>
          <div class="flex items-center gap-2">
            <Icon name="lucide:phone" class="w-4 h-4 text-gray-400" />
            <span>{{ client.phone || 'N/A' }}</span>
          </div>
          <div class="flex items-center gap-2">
            <Icon name="lucide:map-pin" class="w-4 h-4 text-gray-400" />
            <span class="truncate">{{ client.address?.city || client.address?.street || 'N/A' }}</span>
          </div>
        </div>

        <div class="flex justify-between items-center pt-4 border-t border-gray-100">
          <div class="text-center">
            <p class="text-lg font-bold text-gray-900">{{ client.totalInvoices || 0 }}</p>
            <p class="text-xs text-gray-500">Invoices</p>
          </div>
          <div class="text-right">
            <p class="text-lg font-bold text-green-600">₹{{ formatCurrency(client.totalAmount || 0) }}</p>
            <p class="text-xs text-gray-500">Total Value</p>
          </div>
        </div>

        <NuxtLink :to="`/clients/CustomerView/${client._id}`">
          <button class="mt-4 w-full bg-[#00C951] text-white py-3 rounded-lg text-sm font-semibold hover:bg-green-600 transition-colors duration-200">
            View Details
          </button>
        </NuxtLink>
      </div>
    </div>

    <!-- Edit Client Modal -->
    <ClientModal
      :open="showEditModal"
      :client="selectedClient"
      @close="closeEditModal"
      @client-saved="onClientUpdated"
    />

    <!-- Delete Confirmation Dialog -->
    <Dialog :open="showDeleteDialog" @update:open="setDeleteDialog">
      <DialogContent class="sm:max-w-md">
        <DialogHeader>
          <DialogTitle class="flex items-center gap-2 text-red-600">
            <Icon name="lucide:trash" class="w-5 h-5" />
            Delete Client
          </DialogTitle>
          <DialogDescription>
            Are you sure you want to delete <strong>{{ clientToDelete?.name }}</strong>?
            This action cannot be undone and will remove all associated data.
          </DialogDescription>
        </DialogHeader>
        <DialogFooter class="flex gap-2 sm:gap-0">
          <button
            @click="setDeleteDialog(false)"
            class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Cancel
          </button>
          <button
            @click="confirmDelete"
            :disabled="isDeleting"
            class="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
          >
            <Icon name="lucide:trash" class="w-4 h-4" />
            {{ isDeleting ? 'Deleting...' : 'Delete' }}
          </button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useClientStore } from '~/stores/clients'
import { useToast } from 'vue-toastification'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '~/components/ui/dialog'
import ClientModal from './ClientModal.vue'

const searchQuery = ref('')
const sortType = ref('recent')

// Modal states
const showEditModal = ref(false)
const showDeleteDialog = ref(false)
const selectedClient = ref(null)
const clientToDelete = ref(null)
const isDeleting = ref(false)

// Use real client data from store
const clientStore = useClientStore()
const toast = useToast()
const clients = computed(() => clientStore.clients)

// Load clients on mount
onMounted(async () => {
  try {
    await clientStore.refreshClients()
  } catch (error: any) {
    toast.error('Failed to load clients: ' + error.message)
  }
})

// Helper functions
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-IN').format(amount || 0)
}

const statusClass = (status: string) => {
  switch (status) {
    case 'Active':
      return 'bg-green-100 text-green-700'
    case 'Inactive':
      return 'bg-red-100 text-red-700'
    case 'Suspended':
      return 'bg-yellow-100 text-yellow-700'
    default:
      return 'bg-green-100 text-green-700' // Default to active
  }
}

// Filtering & Sorting
const filteredClients = computed(() => {
  if (!clients.value || !Array.isArray(clients.value)) {
    return []
  }

  let result = clients.value.filter((c: any) =>
    c.name?.toLowerCase().includes(searchQuery.value.toLowerCase())
  )

  if (sortType.value === 'recent') {
    result = result.sort((a: any, b: any) =>
      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    )
  } else if (sortType.value === 'oldest') {
    result = result.sort((a: any, b: any) =>
      new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
    )
  } else if (sortType.value === 'business') {
    result = result.filter((c: any) => c.type === 'Business')
  } else if (sortType.value === 'individual') {
    result = result.filter((c: any) => c.type === 'Individual' || !c.type)
  }

  return result
})

// Method for sorting
const sortBy = (type: string) => {
  sortType.value = type
}

// Client management functions
const editClient = (client: any) => {
  selectedClient.value = client
  showEditModal.value = true
}

const closeEditModal = () => {
  showEditModal.value = false
  selectedClient.value = null
}

const onClientUpdated = async () => {
  await clientStore.refreshClients()
  toast.success('Client updated successfully!')
  closeEditModal()
}

const deleteClient = (client: any) => {
  clientToDelete.value = client
  showDeleteDialog.value = true
}

const setDeleteDialog = (open: boolean) => {
  showDeleteDialog.value = open
  if (!open) {
    clientToDelete.value = null
  }
}

const confirmDelete = async () => {
  if (!clientToDelete.value) return

  try {
    isDeleting.value = true
    await clientStore.deleteClient(clientToDelete.value._id)
    toast.success(`Client "${clientToDelete.value.name}" deleted successfully!`)
    setDeleteDialog(false)
  } catch (error: any) {
    toast.error('Failed to delete client: ' + error.message)
  } finally {
    isDeleting.value = false
  }
}

const toggleStatus = async (client: any) => {
  try {
    const currentStatus = client.status || 'Active'
    let newStatus = 'Active'

    // Cycle through statuses: Active -> Inactive -> Suspended -> Active
    if (currentStatus === 'Active') {
      newStatus = 'Inactive'
    } else if (currentStatus === 'Inactive') {
      newStatus = 'Suspended'
    } else {
      newStatus = 'Active'
    }

    await clientStore.updateClient(client._id, { status: newStatus })
    toast.success(`Client status changed to ${newStatus}`)
  } catch (error: any) {
    toast.error('Failed to update client status: ' + error.message)
  }
}
</script>
