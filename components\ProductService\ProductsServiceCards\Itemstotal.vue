<template>
  <!-- Total Items Card -->
  <div class="bg-white text-black rounded-2xl p-4 flex justify-between items-center shadow-sm border border-gray-200 hover:shadow-lg transition-shadow duration-200">
    <div>
      <h2 class="text-gray-600 text-sm font-semibold">Total Items</h2>
      <div class="text-3xl font-bold text-[#00C951]">
        {{ totalItems }}
      </div>
      <p class="text-sm text-gray-400 mt-1">
        {{ productsStore.totalProducts }} products, {{ servicesStore.totalServices }} services
      </p>
    </div>
    <div class="p-1 bg-green-200 rounded-full">
      <Icon name="lucide:badge-check" class="w-10 h-10 text-[#05DF72]"/>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import { useProductsStore } from '~/stores/products'
import { useServicesStore } from '~/stores/services'

const productsStore = useProductsStore()
const servicesStore = useServicesStore()

// Computed total items
const totalItems = computed(() => {
  return (productsStore.totalProducts || 0) + (servicesStore.totalServices || 0)
})
</script>

<style>

</style>