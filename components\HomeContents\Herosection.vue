<template>
 <section class="bg-white font-serif">
    <div class="grid max-w-screen-xl px-4 py-8 mx-auto lg:gap-8 xl:gap-0 lg:py-16 lg:grid-cols-12">
        <div class="mr-auto place-self-center lg:col-span-7">
            <h1 class="max-w-2xl mb-4 text-4xl font-extrabold tracking-tight leading-none md:text-5xl xl:text-6xl ">Invoice Management Made Easy</h1>
            <p class="max-w-2xl mb-6 font-light text-gray-500 lg:mb-8 md:text-lg lg:text-xl ">Streamline your invoicing process with our intuitive platform. Create, manage, and track invoices with just a few clicks.</p>
            
            <ul class="mt-6 space-y-3">
            <li class="flex items-center gap-2">
              <span ><svg
xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
    stroke="#39FF14" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
    class="lucide lucide-circle-check mr-2">
    <circle cx="12" cy="12" r="10"/>
    <path d="m9 12 2 2 4-4"/>
</svg></span> Create professional invoices in seconds
            </li>
            <li class="flex items-center gap-2">
              <span ><svg
xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
    stroke="#39FF14" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
    class="lucide lucide-circle-check mr-2">
    <circle cx="12" cy="12" r="10"/>
    <path d="m9 12 2 2 4-4"/>
</svg></span> Track payments and outstanding invoices
            </li>
            <li class="flex items-center gap-2">
              <span > <svg
xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
    stroke="#39FF14" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
    class="lucide lucide-circle-check mr-2">
    <circle cx="12" cy="12" r="10"/>
    <path d="m9 12 2 2 4-4"/>
</svg></span> Generate insightful financial reports
            </li>
          </ul>

            <div class="flex items-center gap-5 mt-5">
            <a href="register" class="btn-primary flex items-center gap-2">
                Get started
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"/></svg>
            </a>
            <a href="#contactus" class="btn-secondary">
                Speak to Sales
            </a> 
           </div>
        </div>
        <div class=" lg:mt-0 lg:col-span-5 lg:flex">
            <img src="/assets/mobile-invoice.png" alt="mockup">
        </div>                
    </div>
</section>
  </template>
  
  <style scoped>

  </style>
  