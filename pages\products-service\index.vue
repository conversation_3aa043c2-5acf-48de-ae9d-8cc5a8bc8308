<template>
  <div>
    <Heading
      title="Products & Services"
      description="Manage your products and services here"
      icon="lucide:package"
      iconColor="text-green-400"
      bgColor="bg-white"
      :buttons="[
        {
          label: 'View All',
          icon: 'lucide:package',
          bgColor: 'bg-[#00C951]',
          textColor: 'text-white',
          to: '/products-service/ProductCatalog'
        },
        {
          label: 'Add Product',
          icon: 'lucide:package',
          bgColor: 'bg-[#00C951]',
          textColor: 'text-white',
          action: 'openProduct'
        },
        {
          label: 'Add Service',
          icon: 'lucide:book-text',
          bgColor: 'bg-[#00C951]',
          textColor: 'text-white',
          action: 'openService'
        }
      ]"
      @button-click="handleHeadingClick"
    />

    <ProductsServiceCards />

    <ProductsServiceTransactions
      :services="services"
      :products="products"
    />

    <ProductModal
      :open="showProductModal"
      @close="showProductModal = false"
      @add-product="addProduct"
    />

    <ServiceModal
      :open="showServiceModal"
      @close="showServiceModal = false"
      @add-service="addService"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useProductsStore } from '~/stores/products'
import { useServicesStore } from '~/stores/services'
import { useToast } from 'vue-toastification'

definePageMeta({
  middleware: 'auth'
})

const productsStore = useProductsStore()
const servicesStore = useServicesStore()
const toast = useToast()

const showProductModal = ref(false)
const showServiceModal = ref(false)

// Get data from stores instead of static arrays
const products = computed(() => productsStore.products)
const services = computed(() => servicesStore.services)

// Load data on component mount
onMounted(async () => {
  try {
    await Promise.all([
      productsStore.fetchProducts(),
      servicesStore.fetchServices()
    ])
  } catch (error) {
    console.error('Failed to load products and services:', error)
    toast.error('Failed to load data')
  }
})

function handleHeadingClick(action: string) {
  if (action === 'openProduct') {
    showProductModal.value = true
  } else if (action === 'openService') {
    showServiceModal.value = true
  }
}

// Handle successful product creation
async function addProduct(productData: any) {
  try {
    await productsStore.createProduct(productData)
    toast.success('Product created successfully!')
    showProductModal.value = false
  } catch (error) {
    console.error('Failed to create product:', error)
    toast.error('Failed to create product')
  }
}

// Handle successful service creation
async function addService(serviceData: any) {
  try {
    await servicesStore.createService(serviceData)
    toast.success('Service created successfully!')
    showServiceModal.value = false
  } catch (error) {
    console.error('Failed to create service:', error)
    toast.error('Failed to create service')
  }
}
</script>
