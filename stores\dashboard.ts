import { defineStore } from 'pinia'
import { dashboardApi, type DashboardOverview, type RecentActivity } from '~/services/dashboardApi'

interface DashboardState {
  overview: DashboardOverview | null
  recentActivities: RecentActivity[]
  loading: boolean
  error: string | null
  lastUpdated: Date | null
}

export const useDashboardStore = defineStore('dashboard', {
  state: (): DashboardState => ({
    overview: null,
    recentActivities: [],
    loading: false,
    error: null,
    lastUpdated: null
  }),

  getters: {
    // Get total revenue (invoices + services)
    totalRevenue: (state) => {
      return state.overview?.financial.totalRevenue || 0
    },

    // Get net income
    netIncome: (state) => {
      return state.overview?.financial.netIncome || 0
    },

    // Get pending payments
    pendingPayments: (state) => {
      return state.overview?.financial.pendingPayments || 0
    },

    // Get total expenses
    totalExpenses: (state) => {
      return state.overview?.financial.totalExpenses || 0
    },

    // Get total clients
    totalClients: (state) => {
      return state.overview?.clients.total || 0
    },

    // Get active clients
    activeClients: (state) => {
      return state.overview?.clients.active || 0
    },

    // Get total invoices
    totalInvoices: (state) => {
      return state.overview?.invoices.total || 0
    },

    // Get paid invoices
    paidInvoices: (state) => {
      return state.overview?.invoices.paid || 0
    },

    // Get total quotes
    totalQuotes: (state) => {
      return state.overview?.quotes.total || 0
    },

    // Get accepted quotes
    acceptedQuotes: (state) => {
      return state.overview?.quotes.accepted || 0
    },

    // Get total products
    totalProducts: (state) => {
      return state.overview?.products.total || 0
    },

    // Get active products
    activeProducts: (state) => {
      return state.overview?.products.active || 0
    },

    // Get low stock products
    lowStockProducts: (state) => {
      return state.overview?.products.lowStock || 0
    },

    // Get total services
    totalServices: (state) => {
      return state.overview?.services.total || 0
    },

    // Get active services
    activeServices: (state) => {
      return state.overview?.services.active || 0
    },

    // Get recent activities by type
    getActivitiesByType: (state) => (type: string) => {
      return state.recentActivities.filter(activity => activity.type === type)
    },

    // Check if data is stale (older than 5 minutes)
    isDataStale: (state) => {
      if (!state.lastUpdated) return true
      const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000)
      return state.lastUpdated < fiveMinutesAgo
    },

    // Check if loading
    isLoading: (state) => state.loading,

    // Check if there's an error
    hasError: (state) => !!state.error,

    // Check if overview data exists
    hasOverview: (state) => !!state.overview,

    // Check if activities exist
    hasActivities: (state) => state.recentActivities.length > 0
  },

  actions: {
    // Set loading state
    setLoading(loading: boolean) {
      this.loading = loading
    },

    // Set error state
    setError(error: string | null) {
      this.error = error
    },

    // Clear error
    clearError() {
      this.error = null
    },

    // Fetch dashboard overview
    async fetchOverview() {
      try {
        this.setLoading(true)
        this.clearError()

        const response = await dashboardApi.getDashboardOverview()
        
        this.overview = response.data
        this.lastUpdated = new Date()

        console.log('✅ Dashboard overview fetched successfully')
        return response.data
      } catch (error) {
        console.error('❌ Error fetching dashboard overview:', error)
        this.setError(error instanceof Error ? error.message : 'Failed to fetch dashboard overview')
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // Fetch recent activities
    async fetchRecentActivities(limit: number = 10) {
      try {
        this.clearError()

        const response = await dashboardApi.getRecentActivities(limit)
        
        this.recentActivities = response.data

        console.log('✅ Recent activities fetched successfully:', response.data.length)
        return response.data
      } catch (error) {
        console.error('❌ Error fetching recent activities:', error)
        this.setError(error instanceof Error ? error.message : 'Failed to fetch recent activities')
        throw error
      }
    },

    // Fetch all dashboard data
    async fetchDashboardData() {
      try {
        this.setLoading(true)
        this.clearError()

        // Fetch overview and activities in parallel
        const [overview, activities] = await Promise.all([
          dashboardApi.getDashboardOverview(),
          dashboardApi.getRecentActivities(10)
        ])

        this.overview = overview.data
        this.recentActivities = activities.data
        this.lastUpdated = new Date()

        console.log('✅ Dashboard data fetched successfully')
        return {
          overview: overview.data,
          activities: activities.data
        }
      } catch (error) {
        console.error('❌ Error fetching dashboard data:', error)
        this.setError(error instanceof Error ? error.message : 'Failed to fetch dashboard data')
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // Refresh dashboard data if stale
    async refreshIfStale() {
      if (this.isDataStale) {
        await this.fetchDashboardData()
      }
    },

    // Force refresh dashboard data
    async forceRefresh() {
      await this.fetchDashboardData()
    },

    // Update specific overview section
    updateOverviewSection(section: keyof DashboardOverview, data: any) {
      if (this.overview) {
        this.overview[section] = { ...this.overview[section], ...data }
      }
    },

    // Add new activity to the beginning of the list
    addActivity(activity: RecentActivity) {
      this.recentActivities.unshift(activity)
      // Keep only the latest 20 activities
      if (this.recentActivities.length > 20) {
        this.recentActivities = this.recentActivities.slice(0, 20)
      }
    },

    // Remove activity by ID
    removeActivity(id: string) {
      this.recentActivities = this.recentActivities.filter(activity => activity.id !== id)
    },

    // Update activity status
    updateActivityStatus(id: string, status: string) {
      const activity = this.recentActivities.find(activity => activity.id === id)
      if (activity) {
        activity.status = status
      }
    },

    // Clear all data
    clearData() {
      this.overview = null
      this.recentActivities = []
      this.error = null
      this.lastUpdated = null
    },

    // Get dashboard summary for quick stats
    getDashboardSummary() {
      if (!this.overview) return null

      return {
        totalRevenue: this.totalRevenue,
        netIncome: this.netIncome,
        pendingPayments: this.pendingPayments,
        totalClients: this.totalClients,
        totalInvoices: this.totalInvoices,
        totalQuotes: this.totalQuotes,
        totalProducts: this.totalProducts,
        totalServices: this.totalServices,
        lowStockAlerts: this.lowStockProducts,
        lastUpdated: this.lastUpdated
      }
    }
  }
})
