<template>
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8 space-y-8">
    <!-- Invoice Details -->
    <div class="bg-white rounded-xl shadow-sm p-6 sm:p-8">
      <h2 class="text-lg font-semibold mb-6">Invoice Details</h2>
      <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
        <div class="flex items-center">
          <Icon name="lucide:info" class="w-4 h-4 text-blue-600 mr-2" />
          <span class="text-sm text-blue-700">Invoice number will be auto-generated when you create the invoice</span>
        </div>
      </div>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label class="block text-sm font-medium mb-2">Issue Date</label>
          <input type="date" v-model="invoiceDate" class="border border-gray-300 rounded w-full px-3 py-2.5 focus:ring-2 focus:ring-green-500 focus:border-transparent" />
        </div>
        <div>
          <label class="block text-sm font-medium mb-2">Due Date <span class="text-red-500">*</span></label>
          <input type="date" v-model="dueDate" class="border border-gray-300 rounded w-full px-3 py-2.5 focus:ring-2 focus:ring-green-500 focus:border-transparent" />
        </div>
        <div class="md:col-span-2 mt-2">
          <label class="block text-sm font-medium mb-2">Client <span class="text-red-500">*</span></label>
          <select
            v-model="selectedClient"
            @change="selectClient(selectedClient)"
            class="border border-gray-300 rounded w-full px-3 py-2.5 focus:ring-2 focus:ring-green-500 focus:border-transparent"
            :disabled="isLoadingClients"
          >
            <option disabled value="">{{ isLoadingClients ? 'Loading clients...' : 'Select a client' }}</option>
            <option v-for="client in clients" :key="client._id" :value="client._id">
              {{ client.name }} ({{ client.email }})
            </option>
          </select>
        </div>
      </div>
    </div>

    <!-- Item Table -->
    <div class="bg-white p-6 sm:p-8 rounded-xl shadow-sm">
      <div class="flex justify-between items-center mb-6">
        <h2 class="text-lg font-bold">Item Table</h2>
        <button
          class="text-blue-600 text-sm font-medium hover:text-blue-800 hover:bg-blue-50 px-4 py-2 rounded transition-colors duration-200"
          title="Scan item barcode"
        >
          🖨️ Scan Item
        </button>
      </div>

      <!-- Table Header -->
      <div class="grid grid-cols-7 items-center px-4 py-3 bg-gray-100 rounded text-sm font-semibold text-gray-700 border-y">
        <div class="col-span-3">ITEM DETAILS</div>
        <div class="text-center">QUANTITY</div>
        <div class="text-center">RATE</div>
        <div class="text-right">AMOUNT</div>
        <div></div>
      </div>

      <!-- Draggable Items -->
      <draggable v-model="items" item-key="id" class="space-y-4 mt-4" handle=".drag-handle">
        <template #item="{ element: item, index }">
          <div class="grid grid-cols-7 items-start gap-3 border rounded-lg px-4 py-4 bg-white relative">
            <div class="col-span-3 space-y-3">
              <div>
                <div class="flex items-center justify-between mb-2">
                  <label class="block text-xs font-medium text-gray-700">
                    Product Selection
                    <span class="text-green-600 font-normal">(Optional - Manual entry always available)</span>
                  </label>
                  <div class="flex gap-2">
                    <button
                      v-if="item.productId"
                      @click="clearProductSelection(index)"
                      class="text-xs text-blue-600 hover:text-blue-800 underline px-2 py-1 rounded hover:bg-blue-50 transition-colors"
                      type="button"
                      title="Clear selection and enter manually"
                    >
                      <Icon name="lucide:edit-3" class="w-3 h-3 inline mr-1" />
                      Manual Entry
                    </button>
                    <button
                      v-else-if="products.length > 0"
                      @click="enableManualEntry(index)"
                      class="text-xs text-green-600 hover:text-green-800 underline px-2 py-1 rounded hover:bg-green-50 transition-colors"
                      type="button"
                      title="Skip inventory and enter manually"
                    >
                      <Icon name="lucide:pencil" class="w-3 h-3 inline mr-1" />
                      Skip to Manual
                    </button>
                  </div>
                </div>
                <select
                  v-if="products.length > 0"
                  :value="item.productId"
                  @change="selectProduct(index, $event.target.value)"
                  class="w-full text-sm border border-gray-300 rounded px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">🖊️ Enter manually or select from inventory below</option>
                  <option v-for="product in products" :key="product._id" :value="product._id">
                    {{ product.name }} - ₹{{ product.price }}
                  </option>
                </select>
                <div v-else class="text-xs text-gray-500 italic bg-blue-50 p-3 rounded border border-blue-200">
                  <Icon name="lucide:info" class="w-3 h-3 inline mr-1" />
                  No products in inventory - enter product details manually below
                </div>

                <!-- Manual Entry Indicator -->
                <div v-if="!item.productId && item.description" class="text-xs text-green-600 mt-2 flex items-center bg-green-50 p-2 rounded border border-green-200">
                  <Icon name="lucide:check-circle" class="w-3 h-3 mr-1" />
                  Manual entry mode - You're creating a custom product
                </div>
              </div>

              <div>
                <label class="block text-xs font-medium text-gray-700 mb-2">
                  Product Description *
                  <span class="text-green-600 font-normal">(Always editable - customize as needed)</span>
                </label>
                <textarea
                  v-model="item.description"
                  rows="2"
                  class="w-full text-sm border border-gray-300 rounded px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                  placeholder="Enter or customize product description (e.g., 'Premium Wireless Headphones - Noise Cancelling, Bluetooth 5.0, 30hr Battery Life')"
                  :class="{ 'border-red-300 bg-red-50': !item.description.trim() }"
                />
                <div v-if="!item.description.trim()" class="text-xs text-red-500 mt-1 flex items-center">
                  <Icon name="lucide:alert-circle" class="w-3 h-3 mr-1" />
                  Product description is required
                </div>
                <div v-else-if="item.description.trim()" class="text-xs text-green-600 mt-1 flex items-center">
                  <Icon name="lucide:check-circle" class="w-3 h-3 mr-1" />
                  Description looks good
                </div>
              </div>
            </div>
            <div class="flex flex-col items-center justify-center px-2">
              <label class="block text-xs font-medium text-gray-700 mb-2">Quantity</label>
              <input
                type="number"
                v-model.number="item.quantity"
                min="1"
                step="1"
                class="text-sm border border-gray-300 rounded w-16 text-center py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                :class="{ 'border-red-300 bg-red-50': (item.quantity || 0) <= 0 }"
                @input="item.quantity = Number($event.target.value) || 1"
              />
              <span class="text-xs text-gray-500 mt-2">Qty: {{ item.quantity }}</span>
            </div>
            <div class="flex flex-col items-center justify-center px-2">
              <label class="block text-xs font-medium text-gray-700 mb-2">Unit Price</label>
              <input
                type="number"
                v-model.number="item.unitPrice"
                min="0"
                step="0.01"
                class="text-sm border border-gray-300 rounded w-20 text-center py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                :class="{ 'border-red-300 bg-red-50': (item.unitPrice || 0) <= 0 }"
                @input="item.unitPrice = Number($event.target.value) || 0"
              />
              <span class="text-xs text-blue-600 mt-2">₹{{ (item.unitPrice || 0).toFixed(2) }}</span>
            </div>
            <div class="flex flex-col items-end justify-center px-2">
              <label class="block text-xs font-medium text-gray-700 mb-2">Amount</label>
              <div class="text-sm font-semibold text-gray-900 bg-gray-50 px-3 py-2 rounded border">
                ₹{{ formatAmount(item.quantity, item.unitPrice) }}
              </div>
              <span class="text-xs text-gray-500 mt-2">Total</span>
            </div>
            <div class="flex flex-col items-center justify-center space-y-3 px-2">
              <button
                @click="removeItem(index)"
                class="text-red-500 hover:text-red-700 hover:bg-red-50 p-2.5 rounded-full transition-colors duration-200 border border-red-200"
                title="Remove this item"
                :disabled="items.length <= 1"
                :class="{ 'opacity-50 cursor-not-allowed': items.length <= 1 }"
              >
                <Icon name="lucide:trash-2" class="w-4 h-4" />
              </button>
              <span
                class="drag-handle text-gray-400 cursor-move hover:text-gray-600 p-2.5 rounded transition-colors duration-200"
                title="Drag to reorder items"
              >
                <Icon name="lucide:grip-vertical" class="w-4 h-4" />
              </span>
            </div>
          </div>
        </template>
      </draggable>

      <!-- Add Item Actions -->
      <div class="flex flex-wrap gap-4 mt-8">
        <button
          @click="addItem"
          class="text-sm text-blue-600 border border-blue-300 px-5 py-2.5 rounded-md flex items-center gap-2 hover:bg-blue-50 hover:border-blue-400 transition-colors duration-200 font-medium"
          title="Add a new product item to the invoice"
        >
          <Icon name="lucide:plus" class="w-4 h-4" />
          Add New Item
        </button>
        <button
          class="text-sm text-gray-600 border border-gray-300 px-5 py-2.5 rounded-md flex items-center gap-2 hover:bg-gray-50 hover:border-gray-400 transition-colors duration-200 font-medium"
          title="Import multiple items at once (coming soon)"
          disabled
        >
          <Icon name="lucide:package" class="w-4 h-4" />
          Bulk Import
        </button>
      </div>
    </div>

    <!-- Totals and Notes -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <div class="space-y-6">
        <div>
          <label class="block text-sm font-semibold mb-2">Payment Terms</label>
          <input
            v-model="paymentTerms"
            placeholder="e.g., Net 30 days, Due on receipt, 2/10 Net 30"
            class="w-full border border-gray-300 rounded-md px-3 py-2.5 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
        <div>
          <label class="block text-sm font-semibold mb-2">Notes</label>
          <textarea
            rows="4"
            v-model="notes"
            placeholder="Additional terms, conditions, or special instructions for this invoice..."
            class="w-full border border-gray-300 rounded-md px-3 py-2.5 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
          ></textarea>
        </div>
      </div>
      <div class="bg-gray-50 border border-gray-200 rounded-xl p-6 sm:p-8 space-y-5">
        <!-- Subtotal -->
        <div class="flex justify-between items-center py-1">
          <span class="text-sm font-medium text-gray-700">Subtotal:</span>
          <span class="text-sm font-semibold text-gray-900">₹{{ subtotal.toFixed(2) }}</span>
        </div>

        <!-- Discount -->
        <div class="flex justify-between items-center gap-4 py-2">
          <span class="text-sm font-medium text-gray-700 min-w-[80px]">Discount:</span>
          <div class="flex items-center gap-3">
            <input
              type="number"
              v-model.number="discountRate"
              min="0"
              max="100"
              step="0.01"
              class="border border-gray-300 rounded-md px-3 py-2 w-16 text-sm text-center focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="0"
              @input="discountRate = Number($event.target.value) || 0"
            />
            <span class="text-xs text-gray-500 min-w-[12px]">%</span>
            <span class="text-sm font-semibold text-orange-600 min-w-[80px] text-right">₹{{ discountAmount.toFixed(2) }}</span>
          </div>
        </div>

        <!-- Tax -->
        <div class="flex justify-between items-center gap-4 py-2">
          <span class="text-sm font-medium text-gray-700 min-w-[80px]">Tax:</span>
          <div class="flex items-center gap-3">
            <input
              type="number"
              v-model.number="taxRate"
              min="0"
              max="100"
              step="0.01"
              class="border border-gray-300 rounded-md px-3 py-2 w-16 text-sm text-center focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="18"
              @input="taxRate = Number($event.target.value) || 0"
            />
            <span class="text-xs text-gray-500 min-w-[12px]">%</span>
            <span class="text-sm font-semibold text-blue-600 min-w-[80px] text-right">₹{{ taxAmount.toFixed(2) }}</span>
          </div>
        </div>

        <!-- Total -->
        <div class="flex justify-between items-center pt-4 border-t border-gray-300">
          <span class="text-lg font-bold text-gray-900">Total:</span>
          <span class="text-lg font-bold text-green-600">₹{{ total.toFixed(2) }}</span>
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="flex flex-wrap gap-4 pt-2">
      <button
        @click="router.push('/invoices')"
        class="border border-gray-300 px-6 py-3 rounded-md text-sm font-medium hover:bg-gray-50 hover:border-gray-400 transition-colors duration-200"
        :disabled="isSubmitting"
        title="Cancel and return to invoice list"
      >
        Cancel
      </button>
      <button
        @click="saveDraft"
        :disabled="isSubmitting"
        class="bg-gray-900 text-white px-6 py-3 rounded-md text-sm font-medium flex items-center gap-2 hover:bg-gray-800 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
        title="Save invoice as draft"
      >
        <span v-if="isSubmitting">Saving...</span>
        <span v-else>💾 Save as Draft</span>
      </button>
      <button
        @click="createInvoice"
        :disabled="isSubmitting"
        class="bg-green-600 text-white px-6 py-3 rounded-md text-sm font-medium flex items-center gap-2 hover:bg-green-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
        title="Create and send invoice"
      >
        <span v-if="isSubmitting">Creating...</span>
        <span v-else>✉️ Create Invoice</span>
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useToast } from 'vue-toastification'
import draggable from 'vuedraggable'
import { useInvoicesStore } from '~/stores/invoices'
import { useClientStore } from '~/stores/clients'
import { useProductsStore } from '~/stores/products'
import { useAuthStore } from '~/stores/auth'

const router = useRouter()
const toast = useToast()
const invoicesStore = useInvoicesStore()
const clientStore = useClientStore()
const productsStore = useProductsStore()

// Form data
const invoiceDate = ref(new Date().toISOString().substring(0, 10))
const dueDate = ref('')
const selectedClient = ref('')
const selectedClientData = ref(null)

const items = ref([
  { id: Date.now(), type: 'product', description: '', quantity: 1, unitPrice: 0, productId: '' }
])

const discountRate = ref(0)
const taxRate = ref(18)
const notes = ref('')
const paymentTerms = ref('')

// Loading states
const isSubmitting = ref(false)
const isLoadingClients = ref(false)
const isLoadingProducts = ref(false)

// Computed values
const subtotal = computed(() =>
  items.value.reduce((sum, item) => sum + ((item.quantity || 0) * (item.unitPrice || 0)), 0)
)

const discountAmount = computed(() =>
  (subtotal.value * (discountRate.value || 0)) / 100
)

const taxAmount = computed(() =>
  ((subtotal.value - discountAmount.value) * (taxRate.value || 0)) / 100
)

const total = computed(() =>
  subtotal.value - discountAmount.value + taxAmount.value
)

// Get clients and products from stores
const clients = computed(() => clientStore.clients || [])
const products = computed(() => productsStore.products || [])

// Functions
const addItem = () => {
  const newId = Date.now()
  items.value.push({
    id: newId,
    type: 'product',
    description: '',
    quantity: 1,
    unitPrice: 0,
    productId: ''
  })

  // Show helpful message about manual entry
  toast.info('New item added. You can select from inventory or enter product details manually.')
}

const clearProductSelection = (index) => {
  items.value[index].productId = ''
  toast.info('Product selection cleared. Enter details manually in the description field.')
}

const removeItem = (index) => {
  if (items.value.length > 1) {
    items.value.splice(index, 1)
  }
}

const selectClient = (clientId) => {
  const client = clients.value.find(c => c._id === clientId)
  if (client) {
    selectedClientData.value = client
  }
}

const selectProduct = (index, productId) => {
  if (!productId) {
    // Clear product selection - allow manual entry
    items.value[index].productId = ''
    // Don't clear description or price - let user keep manual entries
    toast.info('Product selection cleared. You can now enter custom product details manually.')
    return
  }

  const product = products.value.find(p => p._id === productId)
  if (product) {
    items.value[index].productId = productId
    items.value[index].description = product.name + (product.description ? ` - ${product.description}` : '')
    items.value[index].unitPrice = Number(product.price) || 0

    // Show feedback that product was selected but can still be customized
    toast.success(`Product "${product.name}" selected. You can still customize the description and price as needed.`)
  }
}

// Enhanced function to handle manual product entry
const enableManualEntry = (index) => {
  items.value[index].productId = ''
  toast.info('Manual entry enabled. Enter your custom product details below.')
}

// Function to validate manual entries
const validateManualEntry = (item) => {
  return item.description.trim() && item.quantity > 0 && item.unitPrice >= 0
}

// Form validation
const validateForm = () => {
  if (!selectedClientData.value) {
    toast.error('Please select a client')
    return false
  }

  if (!dueDate.value) {
    toast.error('Please select a due date')
    return false
  }

  if (items.value.length === 0) {
    toast.error('Please add at least one item')
    return false
  }

  for (let i = 0; i < items.value.length; i++) {
    const item = items.value[i]
    if (!item.description.trim()) {
      toast.error(`Please enter description for item ${i + 1}`)
      return false
    }
    if (item.quantity <= 0) {
      toast.error(`Please enter valid quantity for item ${i + 1}`)
      return false
    }
    if (item.unitPrice <= 0) {
      toast.error(`Please enter valid unit price for item ${i + 1}`)
      return false
    }
  }

  return true
}

// Submit invoice
const submitInvoice = async (status = 'draft') => {
  if (!validateForm()) return

  try {
    isSubmitting.value = true

    // Debug auth status before making API call
    const authStore = useAuthStore()
    authStore.debugAuthStatus()

    const invoiceData = {
      clientName: selectedClientData.value.name,
      clientEmail: selectedClientData.value.email,
      clientPhone: selectedClientData.value.phone || '',
      clientAddress: selectedClientData.value.address || {},
      items: items.value.map(item => ({
        description: item.description,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        type: 'product',
        productId: item.productId || undefined
      })),
      taxRate: taxRate.value,
      discountRate: discountRate.value,
      dueDate: dueDate.value,
      issueDate: invoiceDate.value,
      notes: notes.value,
      paymentTerms: paymentTerms.value,
      status: status,
      invoiceType: 'product'
    }

    console.log('📝 Submitting invoice data:', invoiceData)

    await invoicesStore.createInvoice(invoiceData)

    toast.success(`Invoice ${status === 'draft' ? 'saved as draft' : 'created'} successfully!`)

    // Redirect to invoice list or detail page
    router.push('/invoices')

  } catch (error) {
    console.error('Error creating invoice:', error)
    toast.error(error.message || 'Failed to create invoice')
  } finally {
    isSubmitting.value = false
  }
}

const saveDraft = () => submitInvoice('draft')
const createInvoice = () => submitInvoice('sent')

// Load data on mount
onMounted(async () => {
  try {
    isLoadingClients.value = true
    isLoadingProducts.value = true

    // Load clients and products
    await Promise.all([
      clientStore.fetchClients(),
      productsStore.fetchProducts()
    ])

  } catch (error) {
    console.error('Error loading data:', error)
    toast.error('Failed to load clients and products')
  } finally {
    isLoadingClients.value = false
    isLoadingProducts.value = false
  }
})

const formatAmount = (qty, rate) => ((qty || 0) * (rate || 0)).toFixed(2)
</script>

<style scoped>
body {
  background-color: #f9fafb;
}
</style>
