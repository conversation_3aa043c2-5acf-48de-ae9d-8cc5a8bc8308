#!/usr/bin/env node

/**
 * Helper script to get Supabase JWT Secret
 * Run this script to get instructions on finding your JWT secret
 */

console.log('\n🔑 SUPABASE JWT SECRET SETUP GUIDE\n');
console.log('═'.repeat(50));

console.log('\n📋 STEPS TO GET YOUR JWT SECRET:');
console.log('\n1. Go to https://supabase.com');
console.log('2. Sign in and select your project');
console.log('3. Go to Settings → API');
console.log('4. Find the "JWT Secret" section');
console.log('5. Copy the JWT Secret (NOT the anon key)');

console.log('\n📁 UPDATE YOUR BACKEND .env FILE:');
console.log('\nReplace this line in Backend/.env:');
console.log('SUPABASE_JWT_SECRET=your-supabase-jwt-secret-here');
console.log('\nWith your actual JWT secret:');
console.log('SUPABASE_JWT_SECRET=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...');

console.log('\n🔄 RESTART YOUR BACKEND SERVER:');
console.log('cd Backend && npm run dev');

console.log('\n✅ TEST THE CONNECTION:');
console.log('1. Go to http://localhost:3000/api-test');
console.log('2. Click "Test Backend Auth"');
console.log('3. Should show success message');

console.log('\n⚠️  IMPORTANT NOTES:');
console.log('• Use JWT Secret, NOT anon key or service role key');
console.log('• Make sure to restart backend after changing .env');
console.log('• Check browser console for debug logs');

console.log('\n' + '═'.repeat(50));
console.log('Need help? Check Backend/SUPABASE_SETUP.md for detailed guide\n');
