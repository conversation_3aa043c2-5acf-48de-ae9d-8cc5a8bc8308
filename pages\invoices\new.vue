<template>
  <div class="min-h-screen bg-gray-50">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Header -->
      <div class="text-center mb-12">
        <h1 class="text-3xl font-bold text-gray-900 mb-4">Create New Invoice</h1>
        <p class="text-lg text-gray-600">Choose the type of invoice you want to create</p>
      </div>

      <!-- Invoice Type Selection -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-2xl mx-auto">
        <!-- Product Invoice Card -->
        <NuxtLink
          to="/invoices/Create/product"
          class="group bg-white rounded-xl shadow-sm border border-gray-200 p-8 hover:shadow-lg hover:border-green-300 transition-all duration-200 cursor-pointer"
        >
          <div class="text-center">
            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-green-200 transition-colors">
              <Icon name="lucide:package" class="w-8 h-8 text-green-600" />
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-3">Product Invoice</h3>
            <p class="text-gray-600 mb-6">Create invoices for physical products with inventory tracking</p>
            <div class="flex items-center justify-center text-green-600 font-medium group-hover:text-green-700">
              Create Product Invoice
              <Icon name="lucide:arrow-right" class="w-4 h-4 ml-2" />
            </div>
          </div>
        </NuxtLink>

        <!-- Service Invoice Card -->
        <NuxtLink
          to="/invoices/Create/service"
          class="group bg-white rounded-xl shadow-sm border border-gray-200 p-8 hover:shadow-lg hover:border-blue-300 transition-all duration-200 cursor-pointer"
        >
          <div class="text-center">
            <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-blue-200 transition-colors">
              <Icon name="lucide:briefcase" class="w-8 h-8 text-blue-600" />
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-3">Service Invoice</h3>
            <p class="text-gray-600 mb-6">Create invoices for services, consultations, and hourly work</p>
            <div class="flex items-center justify-center text-blue-600 font-medium group-hover:text-blue-700">
              Create Service Invoice
              <Icon name="lucide:arrow-right" class="w-4 h-4 ml-2" />
            </div>
          </div>
        </NuxtLink>
      </div>

      <!-- Quick Info -->
      <div class="mt-12 bg-blue-50 border border-blue-200 rounded-lg p-6 max-w-2xl mx-auto">
        <div class="flex items-start">
          <Icon name="lucide:info" class="w-5 h-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0" />
          <div>
            <h4 class="text-sm font-medium text-blue-900 mb-2">Invoice Features</h4>
            <ul class="text-sm text-blue-700 space-y-1">
              <li>• Auto-generated invoice numbers</li>
              <li>• Professional PDF generation</li>
              <li>• Email sending capabilities</li>
              <li>• Payment tracking and reminders</li>
              <li>• Client management integration</li>
            </ul>
          </div>
        </div>
      </div>

      <!-- Back Button -->
      <div class="text-center mt-8">
        <NuxtLink
          to="/invoices"
          class="inline-flex items-center text-gray-600 hover:text-gray-900 transition-colors"
        >
          <Icon name="lucide:arrow-left" class="w-4 h-4 mr-2" />
          Back to Invoices
        </NuxtLink>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  middleware: 'auth'
})

// Set page title
useHead({
  title: 'Create New Invoice - Invoice @Easy'
})
</script>

<style scoped>
/* Additional hover effects */
.group:hover .transition-all {
  transform: translateY(-2px);
}
</style>
