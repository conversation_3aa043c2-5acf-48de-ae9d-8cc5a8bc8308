<template>
  <div class="md:py-5 p-6">
    <div class="bg-white p-6 rounded-2xl shadow-sm space-y-6 border">
    <!-- Heading -->
    <div class="flex items-center space-x-2">
      <lucide-bell class="text-green-400" />
      <h2 class="text-2xl font-semibold">Notification Preferences</h2>
    </div>

    <!-- Notification Items -->
    <div v-for="(item, i) in notifications" :key="i" class="flex items-center justify-between">
      <div>
        <div class="flex items-center gap-2">
          <component :is="item.icon" v-if="item.icon" class="w-4 h-4 text-muted-foreground" />
          <h3 class="font-medium">{{ item.label }}</h3>
          <span v-if="item.pro" variant="premium" class="text-xs bg-gradient-to-r from-green-400 via-green-500 to-green-600 text-white px-2 py-0.5 rounded-md">Pro</span>
        </div>
        <p class="text-sm text-gray-500">{{ item.description }}</p>
      </div>
      <label class="inline-flex items-center me-5 cursor-pointer">
  <input type="checkbox" value="" class="sr-only peer" :checked="item.enabled" @change="toggle(i)">
  <div class="relative w-11 h-6 bg-gray-200 rounded-full peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600 dark:peer-checked:bg-green-600"></div>
  <span class="ms-3 text-sm font-medium text-gray-900 dark:text-gray-300"></span>
</label>
    </div>

    <!-- Save Button -->
    <Button variant="premium" class="w-full mt-4">
      <lucide-save class="w-5 h-5" />
      Save Notification Settings
    </Button>
  </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import {
  Bell as LucideBell,
  Save as LucideSave,
  Smartphone as LucideSmartphone
} from 'lucide-vue-next'

const notifications = ref([
  {
    label: 'Email Reminders',
    description: 'Receive email notifications for invoice due dates',
    enabled: true
  },
  {
    label: 'WhatsApp Reminders',
    description: 'Send invoice reminders via WhatsApp',
    enabled: false,
    icon: 'LucideSmartphone',
    pro: true
  },
  {
    label: 'Due Invoice Alerts',
    description: 'Get notified when invoices are approaching due date',
    enabled: true
  },
  {
    label: 'Payment Received',
    description: 'Notifications when payments are received',
    enabled: true
  },
  {
    label: 'New Quote Requests',
    description: 'Alerts for new quote requests from clients',
    enabled: true
  }
])

const toggle = (index) => {
  notifications.value[index].enabled = !notifications.value[index].enabled
}
</script>

<style scoped>
/* Optional: custom switch styles */
</style>
