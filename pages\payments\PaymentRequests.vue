<template>
        <!-- Heading -->
      <Heading
    title="payment request"
    description="Track and manage all payment request."
    icon="lucide:indian-rupee"
    iconColor="text-green-400"
    bgColor="bg-white"
    :buttons="[
      {
        label: 'Send payment request',
        icon: 'lucide:send',
        bgColor: 'bg-[#00C951]',
        textColor: 'text-white',
        to: '/payments/Sendpaymentrequest'
      },
      {
        label: 'Back',
        icon: 'lucide:arrow-left',
        bgColor: 'bg-[#00C951]',
        textColor: 'text-white',
        to: '/payments'
      },
    ]"
  />
  <div>
    <PaymentRequests/>
  </div>
  <div>
    <PaymentRequestsTable/>
  </div>
</template>

<script lang="ts" setup>

</script>

<style>

</style>