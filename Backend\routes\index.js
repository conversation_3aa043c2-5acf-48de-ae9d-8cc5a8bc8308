const express = require('express');
const router = express.Router();

// Import route modules
const invoiceRoutes = require('./invoices');
const clientRoutes = require('./clients');
const reportsRoutes = require('./reports');
const authTestRoutes = require('./auth-test');
const quoteRoutes = require('./quotes');
const expenseRoutes = require('./expenses');
const productRoutes = require('./products');
const serviceRoutes = require('./services');
const dashboardRoutes = require('./dashboard');
const userRoutes = require('./userRoutes');

// Mount routes
router.use('/invoices', invoiceRoutes);
router.use('/clients', clientRoutes);
router.use('/reports', reportsRoutes);
router.use('/auth-test', authTestRoutes);
router.use('/quotes', quoteRoutes);
router.use('/expenses', expenseRoutes);
router.use('/products', productRoutes);
router.use('/services', serviceRoutes);
router.use('/dashboard', dashboardRoutes);
router.use('/user', userRoutes);

// Health check route
router.get('/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    message: 'API is healthy',
    timestamp: new Date().toISOString()
  });
});

// API info route
router.get('/', (req, res) => {
  res.json({
    message: 'Backend Invoice API',
    version: '1.0.0',
    endpoints: {
      dashboard: '/api/dashboard',
      invoices: '/api/invoices',
      clients: '/api/clients',
      quotes: '/api/quotes',
      expenses: '/api/expenses',
      products: '/api/products',
      services: '/api/services',
      reports: '/api/reports',
      user: '/api/user',
      authTest: '/api/auth-test',
      health: '/api/health'
    }
  });
});

module.exports = router;
