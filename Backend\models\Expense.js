const mongoose = require('mongoose');

const expenseSchema = new mongoose.Schema({
  userId: {
    type: String,
    required: [true, 'User ID is required'],
    index: true
  },
  expenseId: {
    type: String,
    unique: true,
    index: true
  },
  vendor: {
    type: String,
    required: [true, 'Vendor name is required'],
    trim: true,
    maxlength: [100, 'Vendor name cannot exceed 100 characters']
  },
  category: {
    type: String,
    required: [true, 'Category is required'],
    enum: [
      'Office Supplies',
      'Equipment',
      'Travel',
      'Meals & Entertainment',
      'Marketing',
      'Software & Subscriptions',
      'Utilities',
      'Rent',
      'Insurance',
      'Professional Services',
      'Training & Education',
      'Maintenance & Repairs',
      'Other'
    ],
    default: 'Other'
  },
  description: {
    type: String,
    required: [true, 'Description is required'],
    trim: true,
    maxlength: [500, 'Description cannot exceed 500 characters']
  },
  amount: {
    type: Number,
    required: [true, 'Amount is required'],
    min: [0, 'Amount cannot be negative']
  },
  currency: {
    type: String,
    default: 'USD',
    enum: ['USD', 'EUR', 'GBP', 'CAD', 'AUD', 'INR', 'JPY'],
    uppercase: true
  },
  date: {
    type: Date,
    required: [true, 'Expense date is required'],
    default: Date.now
  },
  status: {
    type: String,
    enum: ['pending', 'approved', 'rejected', 'paid'],
    default: 'pending'
  },
  paymentMethod: {
    type: String,
    enum: ['cash', 'credit_card', 'debit_card', 'bank_transfer', 'check', 'other'],
    default: 'other'
  },
  receiptUrl: {
    type: String,
    trim: true
  },
  notes: {
    type: String,
    trim: true,
    maxlength: [1000, 'Notes cannot exceed 1000 characters']
  },
  tags: [{
    type: String,
    trim: true,
    maxlength: [30, 'Tag cannot exceed 30 characters']
  }],
  isRecurring: {
    type: Boolean,
    default: false
  },
  recurringFrequency: {
    type: String,
    enum: ['weekly', 'monthly', 'quarterly', 'yearly'],
    required: function() {
      return this.isRecurring;
    }
  },
  nextRecurringDate: {
    type: Date,
    required: function() {
      return this.isRecurring;
    }
  },
  // Tax information
  taxRate: {
    type: Number,
    default: 0,
    min: 0,
    max: 100
  },
  taxAmount: {
    type: Number,
    default: 0,
    min: 0
  },
  totalAmount: {
    type: Number,
    default: 0,
    min: 0
  },
  // Approval workflow
  approvedBy: {
    type: String,
    trim: true
  },
  approvedAt: {
    type: Date
  },
  rejectionReason: {
    type: String,
    trim: true,
    maxlength: [500, 'Rejection reason cannot exceed 500 characters']
  }
}, {
  timestamps: true
});

// Pre-save middleware to calculate totals and generate expense ID
expenseSchema.pre('save', function(next) {
  // Generate expense ID if not provided
  if (!this.expenseId) {
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    this.expenseId = `EXP-${year}${month}-${random}`;
  }
  
  // Calculate tax amount
  this.taxAmount = (this.amount * this.taxRate) / 100;
  
  // Calculate total amount
  this.totalAmount = this.amount + this.taxAmount;
  
  // Set approval timestamp
  if (this.status === 'approved' && !this.approvedAt) {
    this.approvedAt = new Date();
  }
  
  next();
});

// Static method to find expenses by user
expenseSchema.statics.findByUser = function(userId, options = {}) {
  const { search, status, category, sort = '-date', limit = 10, skip = 0, startDate, endDate } = options;
  
  let query = { userId };
  
  if (status && status !== 'all') {
    query.status = status;
  }
  
  if (category && category !== 'all') {
    query.category = category;
  }
  
  if (search) {
    query.$or = [
      { vendor: { $regex: search, $options: 'i' } },
      { description: { $regex: search, $options: 'i' } },
      { expenseId: { $regex: search, $options: 'i' } }
    ];
  }
  
  if (startDate || endDate) {
    query.date = {};
    if (startDate) query.date.$gte = new Date(startDate);
    if (endDate) query.date.$lte = new Date(endDate);
  }
  
  return this.find(query)
    .sort(sort)
    .limit(limit)
    .skip(skip);
};

// Static method to get expense statistics
expenseSchema.statics.getStats = function(userId, options = {}) {
  const { startDate, endDate } = options;
  
  let matchQuery = { userId };
  
  if (startDate || endDate) {
    matchQuery.date = {};
    if (startDate) matchQuery.date.$gte = new Date(startDate);
    if (endDate) matchQuery.date.$lte = new Date(endDate);
  }
  
  return this.aggregate([
    { $match: matchQuery },
    {
      $group: {
        _id: null,
        totalExpenses: { $sum: '$totalAmount' },
        totalCount: { $sum: 1 },
        pendingAmount: {
          $sum: { $cond: [{ $eq: ['$status', 'pending'] }, '$totalAmount', 0] }
        },
        approvedAmount: {
          $sum: { $cond: [{ $eq: ['$status', 'approved'] }, '$totalAmount', 0] }
        },
        paidAmount: {
          $sum: { $cond: [{ $eq: ['$status', 'paid'] }, '$totalAmount', 0] }
        },
        rejectedAmount: {
          $sum: { $cond: [{ $eq: ['$status', 'rejected'] }, '$totalAmount', 0] }
        },
        pendingCount: {
          $sum: { $cond: [{ $eq: ['$status', 'pending'] }, 1, 0] }
        },
        approvedCount: {
          $sum: { $cond: [{ $eq: ['$status', 'approved'] }, 1, 0] }
        },
        paidCount: {
          $sum: { $cond: [{ $eq: ['$status', 'paid'] }, 1, 0] }
        },
        rejectedCount: {
          $sum: { $cond: [{ $eq: ['$status', 'rejected'] }, 1, 0] }
        }
      }
    }
  ]);
};

module.exports = mongoose.model('Expense', expenseSchema);
