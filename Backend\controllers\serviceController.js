const Service = require('../models/Service');
const mongoose = require('mongoose');

/**
 * @desc    Get all services for authenticated user
 * @route   GET /api/services
 * @access  Private (Protected by Supabase JWT)
 */
const getServices = async (req, res) => {
  try {
    const userId = req.user.id;
    const {
      page = 1,
      limit = 10,
      search,
      status,
      category,
      pricingType,
      available,
      sort = '-createdAt'
    } = req.query;

    const options = {
      status,
      category,
      pricingType,
      available: available !== undefined ? available === 'true' : undefined,
      search,
      sort,
      limit: parseInt(limit),
      skip: (parseInt(page) - 1) * parseInt(limit)
    };

    // Get services with pagination
    const services = await Service.findByUser(userId, options);
    
    // Build count query
    let countQuery = { userId };
    if (status && status !== 'all') countQuery.status = status;
    if (category && category !== 'all') countQuery.category = category;
    if (pricingType && pricingType !== 'all') countQuery.pricingType = pricingType;
    if (available !== undefined) countQuery.isAvailable = available === 'true';
    if (search) {
      countQuery.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { serviceId: { $regex: search, $options: 'i' } }
      ];
    }

    const totalServices = await Service.countDocuments(countQuery);
    const totalPages = Math.ceil(totalServices / parseInt(limit));

    res.status(200).json({
      success: true,
      count: services.length,
      totalServices,
      totalPages,
      currentPage: parseInt(page),
      data: services,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages,
        totalServices,
        hasNextPage: parseInt(page) < totalPages,
        hasPrevPage: parseInt(page) > 1
      }
    });
  } catch (error) {
    console.error('Get services error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve services',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Get single service by ID
 * @route   GET /api/services/:id
 * @access  Private (Protected by Supabase JWT)
 */
const getService = async (req, res) => {
  try {
    const userId = req.user.id;
    const serviceId = req.params.id;

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(serviceId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid service ID format'
      });
    }

    const service = await Service.findOne({ 
      _id: serviceId, 
      userId 
    });

    if (!service) {
      return res.status(404).json({
        success: false,
        message: 'Service not found'
      });
    }

    res.status(200).json({
      success: true,
      data: service
    });
  } catch (error) {
    console.error('Get service error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve service',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Create new service
 * @route   POST /api/services
 * @access  Private (Protected by Supabase JWT)
 */
const createService = async (req, res) => {
  try {
    const userId = req.user.id;
    
    // Add userId to the service data
    const serviceData = {
      ...req.body,
      userId
    };

    const service = new Service(serviceData);
    const savedService = await service.save();

    res.status(201).json({
      success: true,
      message: 'Service created successfully',
      data: savedService
    });
  } catch (error) {
    console.error('Create service error:', error);
    
    // Handle validation errors
    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map(err => err.message);
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: messages
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to create service',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Update service
 * @route   PUT /api/services/:id
 * @access  Private (Protected by Supabase JWT)
 */
const updateService = async (req, res) => {
  try {
    const userId = req.user.id;
    const serviceId = req.params.id;

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(serviceId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid service ID format'
      });
    }

    // Check if service exists and belongs to user
    const existingService = await Service.findOne({ 
      _id: serviceId, 
      userId 
    });

    if (!existingService) {
      return res.status(404).json({
        success: false,
        message: 'Service not found'
      });
    }

    // Update service
    const updatedService = await Service.findByIdAndUpdate(
      serviceId,
      { ...req.body, updatedAt: new Date() },
      { 
        new: true, 
        runValidators: true 
      }
    );

    res.status(200).json({
      success: true,
      message: 'Service updated successfully',
      data: updatedService
    });
  } catch (error) {
    console.error('Update service error:', error);
    
    // Handle validation errors
    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map(err => err.message);
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: messages
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to update service',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Delete service
 * @route   DELETE /api/services/:id
 * @access  Private (Protected by Supabase JWT)
 */
const deleteService = async (req, res) => {
  try {
    const userId = req.user.id;
    const serviceId = req.params.id;

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(serviceId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid service ID format'
      });
    }

    const service = await Service.findOneAndDelete({ 
      _id: serviceId, 
      userId 
    });

    if (!service) {
      return res.status(404).json({
        success: false,
        message: 'Service not found'
      });
    }

    res.status(200).json({
      success: true,
      message: 'Service deleted successfully',
      data: service
    });
  } catch (error) {
    console.error('Delete service error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete service',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Get service statistics
 * @route   GET /api/services/stats
 * @access  Private (Protected by Supabase JWT)
 */
const getServiceStats = async (req, res) => {
  try {
    const userId = req.user.id;

    const stats = await Service.getStats(userId);

    const result = stats[0] || {
      totalServices: 0,
      activeServices: 0,
      inactiveServices: 0,
      availableServices: 0,
      totalOrders: 0,
      totalRevenue: 0,
      averagePrice: 0,
      averageRating: 0
    };

    res.status(200).json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('Get service stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve service statistics',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Get service categories
 * @route   GET /api/services/categories
 * @access  Private (Protected by Supabase JWT)
 */
const getServiceCategories = async (_req, res) => {
  try {
    const categories = [
      'Consulting',
      'Design',
      'Development',
      'Marketing',
      'Legal',
      'Accounting',
      'Maintenance',
      'Support',
      'Training',
      'Photography',
      'Writing',
      'Translation',
      'Other'
    ];

    res.status(200).json({
      success: true,
      data: categories
    });
  } catch (error) {
    console.error('Get service categories error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve service categories',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Toggle service availability
 * @route   PUT /api/services/:id/availability
 * @access  Private (Protected by Supabase JWT)
 */
const toggleServiceAvailability = async (req, res) => {
  try {
    const userId = req.user.id;
    const serviceId = req.params.id;

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(serviceId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid service ID format'
      });
    }

    const service = await Service.findOne({
      _id: serviceId,
      userId
    });

    if (!service) {
      return res.status(404).json({
        success: false,
        message: 'Service not found'
      });
    }

    // Toggle availability
    const updatedService = await Service.findByIdAndUpdate(
      serviceId,
      {
        isAvailable: !service.isAvailable,
        updatedAt: new Date()
      },
      { new: true, runValidators: true }
    );

    res.status(200).json({
      success: true,
      message: `Service ${updatedService.isAvailable ? 'enabled' : 'disabled'} successfully`,
      data: updatedService
    });
  } catch (error) {
    console.error('Toggle service availability error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to toggle service availability',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

module.exports = {
  getServices,
  getService,
  createService,
  updateService,
  deleteService,
  getServiceStats,
  getServiceCategories,
  toggleServiceAvailability
};
