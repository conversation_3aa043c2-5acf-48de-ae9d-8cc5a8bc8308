<template>
  <Navbar />
  <section class="h-fit bg-gray-100 font-serif">
    <div class="flex min-h-full flex-1 flex-col justify-center px-6 py-12 lg:px-8">
      <div class="sm:mx-auto sm:w-full sm:max-w-sm">
        <img class="mx-auto h-10 w-auto" src="/assets/invoice-easy-logo.png" alt="invoice-logo" />
        <h2 class="mt-3 text-center text-2xl/9 font-bold tracking-tight text-black">
          Welcome Back
        </h2>
        <p class="mt-3 text-center tracking-tight text-black">
          Log in to your Invoice@Easy account
        </p>
      </div>

      <div class="mt-3 sm:mx-auto sm:w-full sm:max-w-sm">
        <!-- Card Wrapper -->
        <div class="border border-gray-300 bg-white shadow-lg z-50 p-6 rounded-xl">
          <form class="space-y-6" @submit.prevent="handleLogin">
            <div>
              <label for="email" class="block text-sm/6 font-medium text-black">Email address</label>
              <div class="mt-2">
                <input
                  id="email"
                  v-model="loginData.email"
                  type="email"
                  name="email"
                  autocomplete="email"
                  class="block w-full rounded-md bg-white px-3 py-1.5 text-base text-black outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6"
                  required
                />
              </div>
            </div>

            <div>
              <div class="flex items-center justify-between">
                <label for="password" class="block text-sm/6 font-medium text-black">Password</label>
                <div class="text-sm">
                  <a href="/Auth/forgot-password" class="font-semibold text-black hover:text-[#00C951]">Forgot password?</a>
                </div>
              </div>
              <div class="mt-2">
                <input
                  id="password"
                  v-model="loginData.password"
                  type="password"
                  name="password"
                  autocomplete="current-password"
                  class="block w-full rounded-md bg-white px-3 py-1.5 text-base text-black outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6"
                  required
                />
              </div>
            </div>

            <div>
              <button
                type="submit"
                class="flex w-full justify-center rounded-md bg-[#00C951] px-3 py-1.5 text-sm/6 font-semibold text-white shadow-xs hover:bg-[#023430] focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
              >
                Sign in
              </button>
            </div>

            <div>
              <div class="flex items-center gap-2 text-gray-500 text-sm my-4">
                <hr class="flex-grow border-gray-700" />
                OR
                <hr class="flex-grow border-gray-700" />
              </div>

              <button
                @click.prevent="signInWithProvider('google')"
                class="w-full flex items-center justify-center font-medium gap-2 border border-black text-black py-2 rounded-md hover:border-white hover:bg-[#023430] hover:text-white transition"
              >
                <Icon name="logos:google-icon" />
                Sign In with Google
              </button>
            </div>
          </form>

          <p class="mt-6 text-center text-sm/6 text-gray-500">
            Don't have an account?
            <a href="/Auth/register" class="font-semibold text-[#00C951] hover:text-[#023430]">Register</a>
          </p>
        </div>
        <!-- End Card Wrapper -->
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
definePageMeta({
  middleware: 'auth-guest'
})
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useToast } from 'vue-toastification'
import { useAuthStore } from '~/stores/auth'
import { useSupabaseClient } from '#imports'

const toast = useToast()
const router = useRouter()
const supabase = useSupabaseClient()
const authStore = useAuthStore()

const loginData = ref({
  email: '',
  password: ''
})

// Email/password login
const handleLogin = async () => {
  try {
    const { error } = await supabase.auth.signInWithPassword({
      email: loginData.value.email,
      password: loginData.value.password
    })

    if (error) {
      toast.error(error.message)
      return
    }

    // The auth store will automatically update via onAuthStateChange
    // But we can also manually fetch to ensure consistency
    await authStore.fetchUser()

    toast.success('Login successful!')

    // Handle redirect parameter
    const route = useRoute()
    const redirectTo = route.query.redirect as string
    const destination = redirectTo && redirectTo !== '/' ? redirectTo : '/'

    router.push(destination)
  } catch (error: any) {
    console.error('Login error:', error)
    toast.error(error.message || 'An unexpected error occurred during login')
  }
}

// OAuth login (Google/GitHub/etc.)
const signInWithProvider = async (provider: 'google' | 'github') => {
  const { error } = await supabase.auth.signInWithOAuth({
    provider,
    options: {
      redirectTo: window.location.origin + '/'
    }
  })

  if (error) {
    toast.error(error.message)
  }
}
</script>
