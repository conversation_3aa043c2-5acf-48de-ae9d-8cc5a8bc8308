<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center gap-4">
      <Button variant="ghost" @click="$router.back()" class="p-2">
        <Icon name="lucide:arrow-left" class="w-5 h-5" />
      </Button>
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Add New Client</h1>
        <p class="text-gray-600">Create a new client for your business</p>
      </div>
    </div>

    <!-- Client Form Card -->
    <Card class="max-w-4xl">
      <CardHeader>
        <CardTitle class="flex items-center gap-2">
          <Icon name="lucide:user-plus" class="w-5 h-5" />
          Client Information
        </CardTitle>
        <CardDescription>
          Fill in the details below to add a new client to your system
        </CardDescription>
      </CardHeader>
      <CardContent>
        <!-- Client Type Selection -->
        <div class="mb-6">
          <label class="block text-sm font-medium mb-3">Client Type</label>
          <div class="flex gap-4">
            <div 
              @click="setType('individual')"
              class="flex-1 p-4 border-2 rounded-lg cursor-pointer transition-all"
              :class="form.type === 'individual' ? 'border-[#00C951] bg-green-50' : 'border-gray-200 hover:border-gray-300'"
            >
              <div class="flex items-center gap-3">
                <Icon name="lucide:user" class="w-6 h-6" :class="form.type === 'individual' ? 'text-[#00C951]' : 'text-gray-400'" />
                <div>
                  <h3 class="font-medium" :class="form.type === 'individual' ? 'text-[#00C951]' : 'text-gray-900'">Individual</h3>
                  <p class="text-sm text-gray-500">Personal client or freelancer</p>
                </div>
              </div>
            </div>
            <div 
              @click="setType('business')"
              class="flex-1 p-4 border-2 rounded-lg cursor-pointer transition-all"
              :class="form.type === 'business' ? 'border-[#00C951] bg-green-50' : 'border-gray-200 hover:border-gray-300'"
            >
              <div class="flex items-center gap-3">
                <Icon name="lucide:building" class="w-6 h-6" :class="form.type === 'business' ? 'text-[#00C951]' : 'text-gray-400'" />
                <div>
                  <h3 class="font-medium" :class="form.type === 'business' ? 'text-[#00C951]' : 'text-gray-900'">Business</h3>
                  <p class="text-sm text-gray-500">Company or organization</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Form Fields -->
        <form @submit.prevent="submit" class="space-y-6">
          <!-- Individual Fields -->
          <div v-if="form.type === 'individual'" class="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium mb-1">First Name *</label>
              <Input v-model="form.firstName" required />
            </div>
            <div>
              <label class="block text-sm font-medium mb-1">Last Name *</label>
              <Input v-model="form.lastName" required />
            </div>
          </div>

          <!-- Business Fields -->
          <div v-if="form.type === 'business'" class="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium mb-1">Company Name *</label>
              <Input v-model="form.companyName" required />
            </div>
            <div>
              <label class="block text-sm font-medium mb-1">Contact Person *</label>
              <Input v-model="form.contactPerson" required />
            </div>
          </div>

          <!-- Contact Information -->
          <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium mb-1">Email *</label>
              <Input v-model="form.email" type="email" required />
            </div>
            <div>
              <label class="block text-sm font-medium mb-1">Phone *</label>
              <Input v-model="form.phone" type="tel" required />
            </div>
          </div>

          <!-- Business Additional Fields -->
          <div v-if="form.type === 'business'" class="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium mb-1">Website</label>
              <Input v-model="form.website" type="url" placeholder="https://example.com" />
            </div>
            <div>
              <label class="block text-sm font-medium mb-1">GST Number</label>
              <Input v-model="form.gstNumber" placeholder="GST registration number" />
            </div>
          </div>

          <!-- Address Information -->
          <div class="space-y-4">
            <h3 class="text-lg font-medium">Address Information</h3>
            <div>
              <label class="block text-sm font-medium mb-1">Street Address</label>
              <Input v-model="form.address.street" placeholder="Street address" />
            </div>
            <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
              <div>
                <label class="block text-sm font-medium mb-1">City</label>
                <Input v-model="form.address.city" />
              </div>
              <div>
                <label class="block text-sm font-medium mb-1">State</label>
                <Input v-model="form.address.state" />
              </div>
              <div>
                <label class="block text-sm font-medium mb-1">Zip Code</label>
                <Input v-model="form.address.zipCode" />
              </div>
            </div>
          </div>

          <!-- Financial Settings -->
          <div class="space-y-4">
            <h3 class="text-lg font-medium">Financial Settings</h3>
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium mb-1">Currency</label>
                <select v-model="form.currency" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#00C951]">
                  <option value="INR">INR (₹)</option>
                  <option value="USD">USD ($)</option>
                  <option value="EUR">EUR (€)</option>
                  <option value="GBP">GBP (£)</option>
                </select>
              </div>
              <div>
                <label class="block text-sm font-medium mb-1">Payment Terms</label>
                <select v-model="form.paymentTerms" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#00C951]">
                  <option value="Net 15">Net 15</option>
                  <option value="Net 30">Net 30</option>
                  <option value="Net 45">Net 45</option>
                  <option value="Net 60">Net 60</option>
                  <option value="Due on Receipt">Due on Receipt</option>
                </select>
              </div>
            </div>
          </div>

          <!-- Notes -->
          <div>
            <label class="block text-sm font-medium mb-1">Notes</label>
            <textarea 
              v-model="form.notes" 
              rows="3" 
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#00C951]"
              placeholder="Additional notes about this client..."
            ></textarea>
          </div>

          <!-- Form Actions -->
          <div class="flex justify-end gap-3 pt-6 border-t">
            <Button type="button" variant="outline" @click="$router.back()">
              Cancel
            </Button>
            <Button type="submit" :disabled="isSubmitting" class="bg-[#00C951] hover:bg-[#00B048]">
              <Icon v-if="isSubmitting" name="lucide:loader-2" class="w-4 h-4 mr-2 animate-spin" />
              <Icon v-else name="lucide:save" class="w-4 h-4 mr-2" />
              {{ isSubmitting ? 'Creating...' : 'Create Client' }}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useClientStore } from '~/stores/clients'
import { useToast } from 'vue-toastification'
import { useRouter } from 'vue-router'

definePageMeta({
  middleware: 'auth'
})

const clientStore = useClientStore()
const toast = useToast()
const router = useRouter()
const isSubmitting = ref(false)

const form = ref({
  type: 'individual',
  email: '',
  phone: '',
  firstName: '',
  lastName: '',
  companyName: '',
  contactPerson: '',
  gstNumber: '',
  website: '',
  address: {
    street: '',
    city: '',
    state: '',
    zipCode: '',
    country: 'India'
  },
  currency: 'INR',
  paymentTerms: 'Net 30',
  status: 'Active',
  notes: ''
})

function setType(type: 'individual' | 'business') {
  form.value.type = type
}

function validateForm() {
  const f = form.value
  return (
    f.email &&
    f.phone &&
    (f.type === 'individual'
      ? f.firstName && f.lastName
      : f.companyName && f.contactPerson)
  )
}

async function submit() {
  if (!validateForm()) {
    toast.error('Please fill in all required fields.')
    return
  }

  try {
    isSubmitting.value = true
    
    // Prepare client data for backend
    const clientData = {
      type: form.value.type === 'individual' ? 'Individual' : 'Business',
      name: form.value.type === 'individual'
        ? `${form.value.firstName} ${form.value.lastName}`.trim()
        : form.value.companyName,
      email: form.value.email,
      phone: form.value.phone,
      company: form.value.type === 'business' ? {
        name: form.value.companyName,
        website: form.value.website,
        taxId: form.value.gstNumber
      } : undefined,
      address: form.value.address,
      currency: form.value.currency,
      paymentTerms: form.value.paymentTerms,
      status: form.value.status,
      notes: form.value.notes
    }

    await clientStore.createClient(clientData)
    toast.success('Client created successfully!')
    router.push('/clients/manage')
  } catch (error: any) {
    toast.error(error.message || 'Failed to create client')
  } finally {
    isSubmitting.value = false
  }
}
</script>
