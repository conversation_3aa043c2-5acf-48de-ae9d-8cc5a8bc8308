<template>
    <div class="p-4 sm:p-6">
      <!-- Search Bar and Filter -->
      <div class="flex items-center justify-between mb-4">
         <!-- search icon -->
         <div class="relative transition-all duration-300 ease-in-out">
          <div
            class="flex items-center border rounded-lg px-3 py-2 w-28 md:w-80 hover:w-56 hover:md:w-96 transition-all duration-300 ease-in-out bg-white shadow-sm focus-within:ring-2"
            style="--tw-ring-color: #05DF72"
          >
            <Icon name="lucide:search" class="w-5 h-5 text-gray-400" />
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Search..."
              class="ml-2 outline-none w-full bg-transparent text-sm text-gray-600"
            />
          </div>
        </div>
        <Button variant="outline" class="ml-4">
          <Filter class="w-4 h-4 mr-2" /> All Statuses
        </Button>
      </div>
  
      <!-- Table -->
      <div class="bg-white shadow rounded-lg overflow-hidden">
        <table class="min-w-full text-sm">
          <thead class="bg-gray-50 text-gray-700 uppercase text-xs">
            <tr>
              <th class="p-4 text-left font-medium">Expense #</th>
              <th class="p-4 text-left font-medium">Category</th>
              <th class="p-4 text-left font-medium">Date</th>
              <th class="p-4 text-left font-medium">Amount</th>
              <th class="p-4 text-left font-medium">Status</th>
              <th class="p-4 text-right font-medium">Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="expense in filteredExpenses"
              :key="expense.id"
              class="border-t hover:bg-gray-50"
            >
              <td class="p-4 font-semibold">EXP-{{ expense.id }}</td>
              <td class="p-4">{{ expense.category }}</td>
              <td class="p-4">{{ expense.date }}</td>
              <td class="p-4">₹{{ expense.amount.toLocaleString() }}</td>
              <td class="p-4">
                <span
                  :class="[
                    'px-2 py-1 rounded-full text-xs font-semibold',
                    expense.status === 'Paid' ? 'bg-green-100 text-green-700' :
                    expense.status === 'Pending' ? 'bg-yellow-100 text-yellow-700' :
                    'bg-red-100 text-red-700'
                  ]"
                >
                  {{ expense.status }}
                </span>
              </td>
  
              <!-- Actions Dropdown -->
              <td class="p-4 text-right relative">
                <MoreVertical class="w-5 h-5 text-gray-400 cursor-pointer" @click="toggleMenu(expense.id)" />
  
                <div
                  v-if="openMenuId === expense.id"
                  class="absolute right-6 mt-2 w-48 bg-white border rounded-lg shadow-xl z-20 overflow-hidden"
                >
                  <button class="flex items-center w-full gap-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" @click="viewExpense(expense)">
                    View Expense
                  </button>
                  <button class="flex items-center w-full gap-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" @click="editExpense(expense)">
                    Edit
                  </button>
                  <button class="flex items-center w-full gap-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" @click="downloadPDF(expense)">
                    <Download class="w-4 h-4" /> Download PDF
                  </button>
                  <button class="flex items-center w-full gap-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" @click="sendToClient(expense)">
                    <Send class="w-4 h-4" /> Send to Client
                  </button>
                  <button class="flex items-center w-full gap-2 px-4 py-2 text-sm text-red-600 hover:bg-red-50" @click="deleteExpense(expense.id)">
                    Delete
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref, computed, onMounted } from 'vue'
  import { Search, Filter, MoreVertical, Download, Send } from 'lucide-vue-next'
  
  // Sample Expense Data
  const expenses = ref([
    { id: 1, category: 'Office Supplies', date: '01 Apr 2025', amount: 2450, status: 'Paid' },
    { id: 2, category: 'Travel', date: '25 Mar 2025', amount: 1275, status: 'Pending' },
    { id: 3, category: 'Internet Bill', date: '05 Apr 2025', amount: 890, status: 'Paid' },
    { id: 4, category: 'Maintenance', date: '15 Mar 2025', amount: 1540, status: 'Overdue' },
    { id: 5, category: 'Software Subscription', date: '02 Apr 2025', amount: 3125, status: 'Paid' },
  ])
  
  const searchQuery = ref('')
  const openMenuId = ref<number | null>(null)
  
  // Filter Expenses
  const filteredExpenses = computed(() => {
    if (!searchQuery.value.trim()) return expenses.value
    return expenses.value.filter(expense =>
      expense.category.toLowerCase().includes(searchQuery.value.toLowerCase())
    )
  })
  
  // Actions
  function toggleMenu(id: number) {
    openMenuId.value = openMenuId.value === id ? null : id
  }
  
  function viewExpense(expense: any) {
    alert(`Viewing Expense: EXP-${expense.id}`)
    openMenuId.value = null
  }
  
  function editExpense(expense: any) {
    alert(`Editing Expense: EXP-${expense.id}`)
    openMenuId.value = null
  }
  
  function downloadPDF(expense: any) {
    alert(`Downloading PDF for: EXP-${expense.id}`)
    openMenuId.value = null
  }
  
  function sendToClient(expense: any) {
    alert(`Sending to Client: EXP-${expense.id}`)
    openMenuId.value = null
  }
  
  function deleteExpense(id: number) {
    if (confirm('Are you sure you want to delete this expense?')) {
      expenses.value = expenses.value.filter(expense => expense.id !== id)
      openMenuId.value = null
    }
  }
  
  // Close menu when clicked outside
  onMounted(() => {
    document.addEventListener('click', (e: any) => {
      if (!e.target.closest('.relative')) {
        openMenuId.value = null
      }
    })
  })
  </script>
  
  