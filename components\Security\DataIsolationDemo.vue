<template>
  <div class="bg-white rounded-lg shadow-sm border p-6">
    <div class="mb-6">
      <h2 class="text-xl font-semibold text-gray-900 mb-2">
        🔒 Data Isolation Verification
      </h2>
      <p class="text-gray-600">
        This component demonstrates that you can only see your own data.
      </p>
    </div>

    <!-- User Information -->
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
      <h3 class="text-lg font-medium text-blue-900 mb-2">Current User</h3>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <span class="text-sm text-blue-700 font-medium">User ID:</span>
          <div class="font-mono text-sm bg-blue-100 p-2 rounded mt-1">
            {{ authStore.user?.id || 'Not authenticated' }}
          </div>
        </div>
        <div>
          <span class="text-sm text-blue-700 font-medium">Email:</span>
          <div class="font-mono text-sm bg-blue-100 p-2 rounded mt-1">
            {{ authStore.user?.email || 'Not authenticated' }}
          </div>
        </div>
      </div>
    </div>

    <!-- Data Summary -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      <div class="bg-green-50 border border-green-200 rounded-lg p-4">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-green-700 font-medium">Your Clients</p>
            <p class="text-2xl font-bold text-green-900">
              {{ clientsStore.totalClients || 0 }}
            </p>
          </div>
          <Icon name="lucide:users" class="w-8 h-8 text-green-600" />
        </div>
      </div>

      <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-purple-700 font-medium">Your Quotes</p>
            <p class="text-2xl font-bold text-purple-900">
              {{ quotesStore.totalQuotes || 0 }}
            </p>
          </div>
          <Icon name="lucide:file-text" class="w-8 h-8 text-purple-600" />
        </div>
      </div>

      <div class="bg-orange-50 border border-orange-200 rounded-lg p-4">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-orange-700 font-medium">Your Expenses</p>
            <p class="text-2xl font-bold text-orange-900">
              {{ expensesStore.totalExpenses || 0 }}
            </p>
          </div>
          <Icon name="lucide:receipt" class="w-8 h-8 text-orange-600" />
        </div>
      </div>

      <div class="bg-indigo-50 border border-indigo-200 rounded-lg p-4">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-indigo-700 font-medium">Your Products</p>
            <p class="text-2xl font-bold text-indigo-900">
              {{ productsStore.totalProducts || 0 }}
            </p>
          </div>
          <Icon name="lucide:package" class="w-8 h-8 text-indigo-600" />
        </div>
      </div>
    </div>

    <!-- Security Features -->
    <div class="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6">
      <h3 class="text-lg font-medium text-gray-900 mb-3">🛡️ Security Features Active</h3>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div class="space-y-2">
          <div class="flex items-center space-x-2">
            <Icon name="lucide:check-circle" class="w-5 h-5 text-green-600" />
            <span class="text-sm text-gray-700">JWT Authentication</span>
          </div>
          <div class="flex items-center space-x-2">
            <Icon name="lucide:check-circle" class="w-5 h-5 text-green-600" />
            <span class="text-sm text-gray-700">User ID Filtering</span>
          </div>
          <div class="flex items-center space-x-2">
            <Icon name="lucide:check-circle" class="w-5 h-5 text-green-600" />
            <span class="text-sm text-gray-700">Ownership Verification</span>
          </div>
        </div>
        <div class="space-y-2">
          <div class="flex items-center space-x-2">
            <Icon name="lucide:check-circle" class="w-5 h-5 text-green-600" />
            <span class="text-sm text-gray-700">Cross-User Access Prevention</span>
          </div>
          <div class="flex items-center space-x-2">
            <Icon name="lucide:check-circle" class="w-5 h-5 text-green-600" />
            <span class="text-sm text-gray-700">Security Auditing</span>
          </div>
          <div class="flex items-center space-x-2">
            <Icon name="lucide:check-circle" class="w-5 h-5 text-green-600" />
            <span class="text-sm text-gray-700">Input Validation</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Test Instructions -->
    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
      <h3 class="text-lg font-medium text-yellow-900 mb-3">🧪 Test Data Isolation</h3>
      <div class="space-y-2 text-sm text-yellow-800">
        <p><strong>To verify data isolation:</strong></p>
        <ol class="list-decimal list-inside space-y-1 ml-4">
          <li>Create some clients, quotes, and expenses with this account</li>
          <li>Log out and create a different user account</li>
          <li>Log in with the new account</li>
          <li>Verify you can only see the new account's data</li>
          <li>The data counters above should show 0 for the new account</li>
        </ol>
        <p class="mt-3 font-medium">
          ✅ Each user's data is completely isolated and secure!
        </p>
      </div>
    </div>

    <!-- Refresh Button -->
    <div class="flex justify-center mt-6">
      <button
        @click="refreshAllData"
        :disabled="isRefreshing"
        class="bg-[#00C951] text-white px-6 py-2 rounded-md hover:bg-[#00B847] transition-colors disabled:opacity-50 flex items-center space-x-2"
      >
        <Icon 
          name="lucide:refresh-cw" 
          class="w-4 h-4"
          :class="{ 'animate-spin': isRefreshing }"
        />
        <span>{{ isRefreshing ? 'Refreshing...' : 'Refresh Data' }}</span>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useAuthStore } from '~/stores/auth'
import { useClientsStore } from '~/stores/clients'
import { useQuotesStore } from '~/stores/quotes'
import { useExpensesStore } from '~/stores/expenses'
import { useProductsStore } from '~/stores/products'

const authStore = useAuthStore()
const clientsStore = useClientsStore()
const quotesStore = useQuotesStore()
const expensesStore = useExpensesStore()
const productsStore = useProductsStore()

const isRefreshing = ref(false)

// Refresh all data to show current user's statistics
const refreshAllData = async () => {
  try {
    isRefreshing.value = true
    
    await Promise.all([
      clientsStore.fetchClientStats(),
      quotesStore.fetchQuoteStats(),
      expensesStore.fetchExpenseStats(),
      productsStore.fetchProductStats()
    ])
  } catch (error) {
    console.error('Failed to refresh data:', error)
  } finally {
    isRefreshing.value = false
  }
}

// Load data on component mount
onMounted(async () => {
  await refreshAllData()
})
</script>
