const express = require('express');
const router = express.Router();
const {
  getQuotes,
  getQuote,
  createQuote,
  updateQuote,
  deleteQuote,
  getQuoteStats,
  convertToInvoice
} = require('../controllers/quoteController');

// Import Supabase authentication middleware
const { verifySupabaseToken } = require('../middleware/supabaseAuth');

// Apply Supabase JWT authentication to all quote routes
router.use(verifySupabaseToken);

/**
 * @route   GET /api/quotes/stats
 * @desc    Get quote statistics for authenticated user
 * @access  Private (Supabase JWT required)
 */
router.get('/stats', getQuoteStats);

/**
 * @route   GET /api/quotes
 * @desc    Get all quotes for authenticated user
 * @access  Private (Supabase JWT required)
 * @query   page, limit, search, status, sort
 */
router.get('/', getQuotes);

/**
 * @route   GET /api/quotes/:id
 * @desc    Get single quote by ID
 * @access  Private (Supabase JWT required)
 */
router.get('/:id', getQuote);

/**
 * @route   POST /api/quotes
 * @desc    Create new quote
 * @access  Private (Supabase JWT required)
 */
router.post('/', createQuote);

/**
 * @route   POST /api/quotes/:id/convert
 * @desc    Convert quote to invoice
 * @access  Private (Supabase JWT required)
 */
router.post('/:id/convert', convertToInvoice);

/**
 * @route   PUT /api/quotes/:id
 * @desc    Update quote by ID
 * @access  Private (Supabase JWT required)
 */
router.put('/:id', updateQuote);

/**
 * @route   DELETE /api/quotes/:id
 * @desc    Delete quote by ID
 * @access  Private (Supabase JWT required)
 */
router.delete('/:id', deleteQuote);

module.exports = router;
