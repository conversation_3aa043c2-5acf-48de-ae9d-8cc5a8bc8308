<template>
  <div class="font-serif flex flex-wrap items-center justify-between gap-3 mb-6">
    <!-- Left: Icon + Title -->
    <div class="flex items-center gap-2">
      <div :class="`p-2 w-fit rounded-md shadow-sm hover:shadow-2xl ${bgColor}`">
        <Icon v-if="icon" :name="icon" :class="`w-8 h-8 ${iconColor}`" />
        <img v-else-if="imageUrl" :src="imageUrl" alt="icon" class="w-8 h-8 object-cover rounded-md" />
      </div>
      <div>
        <h2 class="text-3xl font-bold text-nowrap">{{ title }}</h2>
        <p class="text-sm text-muted-foreground">{{ description }}</p>
      </div>
    </div>

    <!-- Right: Buttons -->
    <div class="flex flex-wrap gap-3 cursor-pointer mt-3 md:mt-0">
      <template v-for="(btn, i) in buttons" :key="i">
        <!-- If 'to' is present, it's a NuxtLink -->
        <NuxtLink
          v-if="btn.to"
          :to="btn.to"
          :class="`flex items-center gap-2 ${btn.bgColor} ${btn.textColor} font-medium py-2 px-4 rounded-md transition hover:opacity-90 cursor-pointer no-underline`"
        >
          <Icon :name="btn.icon" class="w-5 h-5" />
          {{ btn.label }}
        </NuxtLink>

        <!-- If no 'to', it's a button that emits an action -->
        <button
          v-else
          @click="$emit('button-click', btn.action)"
          :class="`flex items-center gap-2 ${btn.bgColor} ${btn.textColor} font-medium py-2 px-4 rounded-md transition hover:opacity-90`"
        >
          <Icon :name="btn.icon" class="w-5 h-5" />
          {{ btn.label }}
        </button>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
interface ButtonConfig {
  label: string
  icon: string
  bgColor: string
  textColor: string
  action?: string
  to?: string
}

interface Props {
  title: string
  description: string
  icon?: string
  imageUrl?: string
  iconColor: string
  bgColor: string
  buttons: ButtonConfig[]
}

defineProps<Props>()
defineEmits(['button-click'])
</script>
