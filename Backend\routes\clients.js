const express = require('express');
const router = express.Router();
const {
  getClients,
  getClient,
  createClient,
  updateClient,
  deleteClient,
  getClientStats,
  fixExistingClients
} = require('../controllers/clientController');

// Import Supabase authentication middleware
const { verifySupabaseToken, verifyRole } = require('../middleware/supabaseAuth');

// Import enhanced data isolation middleware
const { dataIsolationSuite } = require('../middleware/dataIsolation');
const Client = require('../models/Client');

/**
 * @route   GET /api/clients/reports/test
 * @desc    Get comprehensive client reports data (test endpoint - no auth)
 * @access  Public (for testing only)
 */
router.get('/reports/test', require('../controllers/clientController').getClientReportsTest);

// Apply Supabase JWT authentication to all other client routes
router.use(verifySupabaseToken);

// Create enhanced data isolation middleware suite for Client model
const clientSecurity = dataIsolationSuite(Client, {
  resourceType: 'client',
  enableAudit: true
});

/**
 * @route   GET /api/clients/stats
 * @desc    Get client statistics for authenticated user
 * @access  Private (Supabase JWT required + Data Isolation)
 */
router.get('/stats', clientSecurity.listProtection, getClientStats);

/**
 * @route   GET /api/clients/reports
 * @desc    Get comprehensive client reports data
 * @access  Private (Supabase JWT required + Data Isolation)
 */
router.get('/reports', clientSecurity.listProtection, require('../controllers/clientController').getClientReports);

/**
 * @route   GET /api/clients
 * @desc    Get all clients for authenticated user
 * @access  Private (Supabase JWT required + Data Isolation)
 * @query   page, limit, search, status, sort
 */
router.get('/', clientSecurity.listProtection, getClients);

/**
 * @route   GET /api/clients/:id/stats
 * @desc    Get detailed statistics for a specific client
 * @access  Private (Supabase JWT required + Data Isolation)
 */
router.get('/:id/stats', clientSecurity.ensureOwnership, async (req, res) => {
  try {
    const userId = req.user.id;
    const clientId = req.params.id;

    // Validate ObjectId
    const mongoose = require('mongoose');
    if (!mongoose.Types.ObjectId.isValid(clientId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid client ID format'
      });
    }

    // Get client details
    const Client = require('../models/Client');
    const client = await Client.findOne({
      _id: clientId,
      userId
    });

    if (!client) {
      return res.status(404).json({
        success: false,
        message: 'Client not found'
      });
    }

    // Calculate real invoice statistics from database
    const Invoice = require('../models/Invoice');

    // Get all invoices for this client
    const clientInvoices = await Invoice.find({
      userId,
      $or: [
        { clientEmail: client.email },
        { clientName: client.name }
      ]
    }).sort({ createdAt: -1 });

    console.log(`📊 Found ${clientInvoices.length} invoices for client ${client.name} (${client.email})`);

    // Calculate statistics
    const stats = {
      totalInvoices: clientInvoices.length,
      paidInvoices: clientInvoices.filter(inv => inv.isPaid || inv.status === 'paid').length,
      pendingInvoices: clientInvoices.filter(inv => !inv.isPaid && (inv.status === 'sent' || inv.status === 'draft')).length,
      overdueInvoices: clientInvoices.filter(inv => !inv.isPaid && inv.status === 'overdue').length,
      totalRevenue: clientInvoices.filter(inv => inv.isPaid || inv.status === 'paid').reduce((sum, inv) => sum + (inv.total || 0), 0),
      totalOutstanding: clientInvoices.filter(inv => !inv.isPaid && inv.status !== 'cancelled').reduce((sum, inv) => sum + (inv.total || 0), 0),
      productRevenue: 0,
      serviceRevenue: 0
    };

    // Calculate product vs service revenue
    clientInvoices.filter(inv => inv.isPaid || inv.status === 'paid').forEach(invoice => {
      invoice.items.forEach(item => {
        if (item.type === 'product') {
          stats.productRevenue += item.total || 0;
        } else if (item.type === 'service') {
          stats.serviceRevenue += item.total || 0;
        }
      });
    });

    // Generate recent activity from invoices
    const recentActivity = clientInvoices.slice(0, 5).map(invoice => {
      let description = '';
      if (invoice.status === 'paid' || invoice.isPaid) {
        description = `Invoice ${invoice.invoiceNumber} paid`;
      } else if (invoice.status === 'overdue') {
        description = `Invoice ${invoice.invoiceNumber} is overdue`;
      } else if (invoice.status === 'sent') {
        description = `Invoice ${invoice.invoiceNumber} sent`;
      } else {
        description = `Invoice ${invoice.invoiceNumber} created`;
      }

      return {
        type: 'invoice',
        description,
        date: invoice.createdAt || invoice.issueDate,
        amount: invoice.total
      };
    });

    console.log('📈 Calculated stats:', stats);

    res.status(200).json({
      success: true,
      data: {
        client,
        invoiceStats: stats,
        recentActivity
      }
    });
  } catch (error) {
    console.error('Get client detailed stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve client detailed statistics',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @route   GET /api/clients/:id
 * @desc    Get single client by ID
 * @access  Private (Supabase JWT required + Ownership Verification)
 */
router.get('/:id', clientSecurity.ensureOwnership, getClient);

/**
 * @route   POST /api/clients
 * @desc    Create new client
 * @access  Private (Supabase JWT required + Auto UserId Assignment)
 */
router.post('/', clientSecurity.createProtection, createClient);

/**
 * @route   POST /api/clients/fix-existing
 * @desc    Fix existing clients (temporary endpoint)
 * @access  Private (Supabase JWT required)
 */
router.post('/fix-existing', fixExistingClients);

/**
 * @route   PUT /api/clients/:id
 * @desc    Update client by ID
 * @access  Private (Supabase JWT required + Ownership Verification)
 */
router.put('/:id', clientSecurity.updateProtection, updateClient);

/**
 * @route   DELETE /api/clients/:id
 * @desc    Delete client by ID
 * @access  Private (Supabase JWT required + Ownership Verification)
 */
router.delete('/:id', clientSecurity.deleteProtection, deleteClient);

module.exports = router;
