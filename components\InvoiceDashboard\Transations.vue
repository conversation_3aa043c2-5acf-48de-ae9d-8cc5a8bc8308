<template>
    <div class="p-4 sm:p-6">
      <div class="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
        <!-- search icon -->
        <div class="relative transition-all duration-300 ease-in-out">
          <div
            class="flex items-center border rounded-lg px-3 py-2 w-28 md:w-80 hover:w-56 hover:md:w-96 transition-all duration-300 ease-in-out bg-white shadow-sm focus-within:ring-2"
            style="--tw-ring-color: #05DF72"
          >
            <Icon name="lucide:search" class="w-5 h-5 text-gray-400" />
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Search invoices, clients..."
              class="ml-2 outline-none w-full bg-transparent text-sm text-gray-600"
            />
            <button
              v-if="searchQuery"
              @click="clearSearch"
              class="ml-2 text-gray-400 hover:text-gray-600 transition-colors"
              title="Clear search"
            >
              <Icon name="lucide:x" class="w-4 h-4" />
            </button>
          </div>
        </div>

        <!-- Combined Status and Sort Filters -->
        <div class="flex items-center gap-3">
          <!-- Status Filter -->
          <select
            v-model="statusFilter"
            class="border border-gray-300 rounded-lg px-3 py-2 text-sm bg-white min-w-[120px]"
          >
            <option value="">All Status</option>
            <option value="draft">Draft</option>
            <option value="sent">Sent</option>
            <option value="paid">Paid</option>
            <option value="overdue">Overdue</option>
            <option value="cancelled">Cancelled</option>
          </select>

          <!-- Sort Dropdown -->
          <DropdownMenu>
            <DropdownMenuTrigger as-child>
              <Button variant="outline" class="min-w-[100px]">
                <Filter class="w-4 h-4 mr-2" />
                Sort
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem @click="sortBy('-createdAt')">Newest First</DropdownMenuItem>
              <DropdownMenuItem @click="sortBy('createdAt')">Oldest First</DropdownMenuItem>
              <DropdownMenuItem @click="sortBy('-total')">Highest Amount</DropdownMenuItem>
              <DropdownMenuItem @click="sortBy('total')">Lowest Amount</DropdownMenuItem>
              <DropdownMenuItem @click="sortBy('clientName')">Client A-Z</DropdownMenuItem>
              <DropdownMenuItem @click="sortBy('-clientName')">Client Z-A</DropdownMenuItem>
              <DropdownMenuItem @click="sortBy('status')">Status</DropdownMenuItem>
              <DropdownMenuItem @click="sortBy('-dueDate')">Due Date (Latest)</DropdownMenuItem>
              <DropdownMenuItem @click="sortBy('dueDate')">Due Date (Earliest)</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <!-- Simple Results Summary -->
      <div v-if="invoices.length > 0 && (searchQuery || statusFilter)" class="mb-4 text-sm text-gray-600">
        Showing {{ invoices.length }} filtered result{{ invoices.length !== 1 ? 's' : '' }}
        <span v-if="searchQuery" class="text-green-600">for "{{ searchQuery }}"</span>
        <span v-if="statusFilter" class="text-blue-600 capitalize">• {{ statusFilter }} invoices</span>
      </div>

      <div class="overflow-auto rounded-xl border">
        <table class="min-w-full text-sm text-left">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 font-medium text-gray-700">Invoice ID</th>
              <th class="px-6 py-3 font-medium text-gray-700">Client</th>
              <th class="px-6 py-3 font-medium text-gray-700">Date</th>
              <th class="px-6 py-3 font-medium text-gray-700">Due Date</th>
              <th class="px-6 py-3 font-medium text-gray-700">Amount</th>
              <th class="px-6 py-3 font-medium text-gray-700">Status</th>
              <th class="px-6 py-3"></th>
            </tr>
          </thead>
          <tbody>
            <!-- Loading state -->
            <tr v-if="isLoading">
              <td colspan="7" class="px-6 py-8 text-center">
                <div class="flex items-center justify-center">
                  <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-green-500"></div>
                  <span class="ml-2 text-gray-500">Loading invoices...</span>
                </div>
              </td>
            </tr>

            <!-- No invoices state -->
            <tr v-else-if="invoices.length === 0">
              <td colspan="7" class="px-6 py-8 text-center text-gray-500">
                No invoices found. Create your first invoice to get started.
              </td>
            </tr>

            <!-- Invoice rows -->
            <tr
              v-else
              v-for="invoice in invoices"
              :key="invoice._id"
              class="border-t hover:bg-gray-50"
            >
              <td class="px-6 py-4 font-bold text-gray-800">{{ invoice.invoiceNumber }}</td>
              <td class="px-6 py-4">{{ invoice.clientName }}</td>
              <td class="px-6 py-4">{{ formatDate(invoice.issueDate || invoice.createdAt) }}</td>
              <td class="px-6 py-4">{{ formatDate(invoice.dueDate) }}</td>
              <td class="px-6 py-4 font-medium">₹{{ formatCurrency(invoice.total) }}</td>
              <td class="px-6 py-4">
                <div class="flex items-center space-x-2">
                  <span
                    :class="[
                      'inline-block px-2 py-1 rounded-full text-xs font-semibold capitalize',
                      getStatusClass(getDisplayStatus(invoice))
                    ]"
                  >
                    {{ getDisplayStatus(invoice) }}
                  </span>
                  <span v-if="isOverdue(invoice)" class="inline-block px-2 py-1 rounded-full text-xs font-semibold bg-red-100 text-red-800">
                    Overdue
                  </span>
                </div>
              </td>
              <td class="px-6 py-4 text-right relative">
                <DropdownMenu>
                  <DropdownMenuTrigger as-child>
                    <Button variant="ghost" size="icon" :disabled="isDeleting">
                      <MoreVertical class="w-4 h-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem @click="viewInvoice(invoice)">View</DropdownMenuItem>
                    <DropdownMenuItem @click="downloadPDF(invoice)">Download PDF</DropdownMenuItem>
                    <DropdownMenuItem @click="printInvoice(invoice)">Print Preview</DropdownMenuItem>
                    <DropdownMenuItem @click="sendEmail(invoice)">Send Email</DropdownMenuItem>
                    <DropdownMenuItem @click="editInvoice(invoice)">Edit</DropdownMenuItem>
                    <DropdownMenuItem
                      @click="markAsPaid(invoice)"
                      :class="invoice.isPaid ? 'text-orange-600' : 'text-green-600'"
                      :disabled="isUpdatingPayment"
                    >
                      <Icon
                        v-if="!isUpdatingPayment"
                        :name="invoice.isPaid ? 'lucide:x-circle' : 'lucide:check-circle'"
                        class="w-4 h-4 mr-2"
                      />
                      <Icon
                        v-else
                        name="lucide:loader-2"
                        class="w-4 h-4 mr-2 animate-spin"
                      />
                      {{ isUpdatingPayment ? 'Updating...' : (invoice.isPaid ? 'Mark as Unpaid' : 'Mark as Paid') }}
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      class="text-red-600"
                      @click="deleteInvoice(invoice)"
                      :disabled="isDeleting"
                    >
                      Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </td>
            </tr>
          </tbody>
        </table>
      </div>


    </div>
  </template>
  
  <script setup>
  import { ref, computed, onMounted, watch } from 'vue'
  import { useRouter } from 'vue-router'
  import { useToast } from 'vue-toastification'
  import { MoreVertical, Filter } from 'lucide-vue-next'
  import { useInvoicesStore } from '~/stores/invoices'

  const router = useRouter()
  const toast = useToast()
  const invoicesStore = useInvoicesStore()

  const searchQuery = ref('')
  const sortOption = ref('-createdAt')
  const statusFilter = ref('')
  const isDeleting = ref(false)
  const isUpdatingPayment = ref(false)

  // Computed properties
  const invoices = computed(() => invoicesStore.invoices)
  const isLoading = computed(() => invoicesStore.isLoading)
  const pagination = computed(() => invoicesStore.pagination)

  // Watch for search changes and debounce
  let searchTimeout
  watch(searchQuery, (newQuery) => {
    clearTimeout(searchTimeout)
    searchTimeout = setTimeout(() => {
      invoicesStore.searchInvoices(newQuery)
    }, 300)
  })

  // Watch for status filter changes
  watch(statusFilter, (newStatus) => {
    invoicesStore.filterByStatus(newStatus)
  })

  function sortBy(option) {
    sortOption.value = option
    invoicesStore.sortInvoices(option)
  }

  const clearSearch = () => {
    searchQuery.value = ''
    invoicesStore.searchInvoices('')
  }

  // Invoice operations
  const viewInvoice = (invoice) => {
    console.log('🔍 Viewing invoice:', {
      id: invoice._id,
      number: invoice.invoiceNumber,
      client: invoice.clientName,
      total: invoice.total
    })

    if (!invoice._id) {
      console.error('❌ Invoice ID is missing:', invoice)
      toast.error('Cannot view invoice: ID is missing')
      return
    }

    router.push(`/invoices/${invoice._id}`)
  }

  const editInvoice = (invoice) => {
    const invoiceType = invoice.invoiceType || 'product'
    router.push(`/invoices/edit/${invoiceType}/${invoice._id}`)
  }

  const deleteInvoice = async (invoice) => {
    if (!confirm(`Are you sure you want to delete invoice ${invoice.invoiceNumber}?`)) {
      return
    }

    try {
      isDeleting.value = true
      await invoicesStore.deleteInvoice(invoice._id)
      toast.success('Invoice deleted successfully')
    } catch (error) {
      console.error('Error deleting invoice:', error)
      toast.error(error.message || 'Failed to delete invoice')
    } finally {
      isDeleting.value = false
    }
  }

  const markAsPaid = async (invoice) => {
    if (isUpdatingPayment.value) return

    try {
      isUpdatingPayment.value = true
      const newPaidStatus = !invoice.isPaid

      console.log(`💰 Updating payment status: ${invoice.invoiceNumber} - ${newPaidStatus ? 'PAID' : 'UNPAID'}`)

      // Call backend API through store
      const updatedInvoice = await invoicesStore.updatePaymentStatus(invoice._id, newPaidStatus)

      if (updatedInvoice) {
        console.log(`✅ Payment status updated successfully: isPaid=${updatedInvoice.isPaid}, status=${updatedInvoice.status}`)

        // Show success message with status details
        const statusText = newPaidStatus ? 'paid' : 'unpaid'
        toast.success(`Invoice marked as ${statusText} successfully! Status: ${updatedInvoice.status}`)
      } else {
        toast.success(`Invoice marked as ${newPaidStatus ? 'paid' : 'unpaid'} successfully!`)
      }

    } catch (error) {
      console.error('Payment status update failed:', error)
      toast.error(error.message || 'Failed to update payment status. Please try again.')
    } finally {
      isUpdatingPayment.value = false
    }
  }

  const sendEmail = async (invoice) => {
    try {
      const emailData = {
        subject: `Invoice ${invoice.invoiceNumber}`,
        message: `Dear ${invoice.clientName},\n\nPlease find attached invoice ${invoice.invoiceNumber} for the amount of ₹${invoice.total.toFixed(2)}.\n\nThank you for your business!`,
        recipientEmail: invoice.clientEmail
      }

      await invoicesStore.sendEmail(invoice._id, emailData)
      toast.success('Invoice email sent successfully!')
    } catch (error) {
      console.error('Error sending email:', error)
      toast.error('Failed to send email')
    }
  }

  const downloadPDF = async (invoice) => {
    try {
      const result = await invoicesStore.generatePDF(invoice._id)

      if (result && result.success) {
        toast.success(`PDF download started! Check your Downloads folder for ${invoice.invoiceNumber}.pdf`)
      } else {
        toast.success('PDF download started!')
      }
    } catch (error) {
      console.error('Error generating PDF:', error)
      toast.error(error.message || 'Failed to generate PDF')
    }
  }

  const printInvoice = async (invoice) => {
    try {
      await invoicesStore.printInvoice(invoice._id)
      toast.success('Print preview opened!')
    } catch (error) {
      console.error('Error printing invoice:', error)
      toast.error('Failed to open print preview')
    }
  }

  // Helper function to determine display status based on overdue logic
  const getDisplayStatus = (invoice) => {
    // If paid, always show paid status
    if (invoice.isPaid) {
      return 'paid'
    }

    // For draft and cancelled, show as-is
    if (invoice.status === 'draft' || invoice.status === 'cancelled') {
      return invoice.status
    }

    // For other statuses, show the current status (overdue will be shown as separate badge)
    return invoice.status === 'overdue' ? 'sent' : invoice.status
  }

  // Helper function to check if invoice is overdue
  const isOverdue = (invoice) => {
    if (invoice.isPaid) return false

    const currentDate = new Date()
    const dueDate = new Date(invoice.dueDate)

    return currentDate > dueDate && invoice.status !== 'draft' && invoice.status !== 'cancelled'
  }

  // Helper function to generate printable HTML
  const generatePrintableHTML = (invoice) => {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Invoice ${invoice.invoiceNumber}</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          .header { text-align: center; margin-bottom: 30px; }
          .invoice-details { display: flex; justify-content: space-between; margin-bottom: 30px; }
          .items-table { width: 100%; border-collapse: collapse; margin-bottom: 30px; }
          .items-table th, .items-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
          .items-table th { background-color: #f2f2f2; }
          .totals { text-align: right; }
          .total-line { margin: 5px 0; }
          .total-final { font-weight: bold; font-size: 18px; border-top: 2px solid #000; padding-top: 10px; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>INVOICE</h1>
          <h2>${invoice.invoiceNumber}</h2>
        </div>

        <div class="invoice-details">
          <div>
            <h3>Bill To:</h3>
            <p><strong>${invoice.clientName}</strong></p>
            <p>${invoice.clientEmail}</p>
            ${invoice.clientPhone ? `<p>${invoice.clientPhone}</p>` : ''}
          </div>
          <div>
            <p><strong>Issue Date:</strong> ${formatDate(invoice.issueDate)}</p>
            <p><strong>Due Date:</strong> ${formatDate(invoice.dueDate)}</p>
            <p><strong>Status:</strong> ${getDisplayStatus(invoice)}</p>
          </div>
        </div>

        <table class="items-table">
          <thead>
            <tr>
              <th>Description</th>
              <th>Quantity</th>
              <th>Unit Price</th>
              <th>Total</th>
            </tr>
          </thead>
          <tbody>
            ${invoice.items.map(item => `
              <tr>
                <td>${item.description}</td>
                <td>${item.quantity}</td>
                <td>₹${formatCurrency(item.unitPrice)}</td>
                <td>₹${formatCurrency(item.total || (item.quantity * item.unitPrice))}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>

        <div class="totals">
          <div class="total-line">Subtotal: ₹${formatCurrency(invoice.subtotal)}</div>
          ${invoice.discountRate > 0 ? `<div class="total-line">Discount (${invoice.discountRate}%): -₹${formatCurrency(invoice.discountAmount)}</div>` : ''}
          ${invoice.taxRate > 0 ? `<div class="total-line">Tax (${invoice.taxRate}%): ₹${formatCurrency(invoice.taxAmount)}</div>` : ''}
          <div class="total-final">Total: ₹${formatCurrency(invoice.total)}</div>
        </div>

        ${invoice.notes ? `
          <div style="margin-top: 30px;">
            <h3>Notes:</h3>
            <p>${invoice.notes}</p>
          </div>
        ` : ''}
      </body>
      </html>
    `
  }

  // Format currency helper
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount || 0)
  }

  // Format date helper
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  // Get status badge class
  const getStatusClass = (status) => {
    const classes = {
      'paid': 'bg-green-100 text-green-800',
      'sent': 'bg-blue-100 text-blue-800',
      'pending': 'bg-blue-100 text-blue-800',
      'draft': 'bg-gray-100 text-gray-800',
      'overdue': 'bg-red-100 text-red-800',
      'cancelled': 'bg-red-100 text-red-800'
    }
    return classes[status?.toLowerCase()] || 'bg-gray-100 text-gray-800'
  }

  // Load invoices on component mount
  onMounted(async () => {
    try {
      await invoicesStore.fetchInvoices()
    } catch (error) {
      console.error('Failed to load invoices:', error)
      toast.error('Failed to load invoices')
    }
  })
  </script>
  