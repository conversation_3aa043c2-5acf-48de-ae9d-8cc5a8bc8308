<template>
  <div class="min-h-screen bg-gray-50 px-6 py-10">
    <!-- Page Header -->
    <div class="flex items-center justify-between mb-6">
      <h1 class="text-2xl font-bold text-gray-900">Create Quote</h1>
      <div class="flex gap-3">
        <button class="flex items-center gap-1 px-4 py-2 rounded-lg text-sm bg-white border hover:bg-gray-100 text-gray-800">
          <Eye class="w-4 h-4" /> Preview
        </button>
        <button class="flex items-center gap-1 px-4 py-2 rounded-lg text-sm bg-blue-900 text-white hover:bg-blue-800">
          <Save class="w-4 h-4" /> Save
        </button>
        <button class="flex items-center gap-1 px-4 py-2 rounded-lg text-sm bg-white border hover:bg-gray-100 text-gray-800">
          Cancel
        </button>
      </div>
    </div>

    <!-- Quote Details Card -->
    <div class="bg-white rounded-xl shadow-sm p-6">
      <h2 class="text-xl font-semibold text-gray-800 mb-6">Quote Details</h2>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
        <!-- Quote Number -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Quote Number</label>
          <input type="text" class="w-full rounded-md border-gray-300 focus:ring-2 focus:ring-blue-500" :value="quoteNumber" readonly />
        </div>

        <!-- Date -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Date</label>
          <input type="date" class="w-full rounded-md border-gray-300 focus:ring-2 focus:ring-blue-500" :value="currentDate" />
        </div>

        <!-- Valid Until -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Valid Until</label>
          <input type="date" class="w-full rounded-md border-gray-300 focus:ring-2 focus:ring-blue-500" />
        </div>
      </div>

      <!-- Client Dropdown -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-1">Client</label>
        <select class="w-full rounded-md border-gray-300 focus:ring-2 focus:ring-blue-500">
          <option selected disabled>Select a client</option>
          <!-- Populate dynamically -->
        </select>
      </div>
    </div>

    <!-- Items Section -->
    <div class="bg-white rounded-xl shadow-sm p-6 mt-6">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-semibold text-gray-800">Items</h2>
        <button
          @click="addItem"
          class="flex items-center gap-1 px-3 py-2 rounded-md border text-sm text-blue-600 hover:bg-blue-50"
        >
          <Plus class="w-4 h-4" /> Add Item
        </button>
      </div>

      <div v-for="(item, index) in items" :key="index" class="grid grid-cols-12 gap-3 items-center mb-3">
        <input
          v-model="item.description"
          placeholder="Item description"
          class="col-span-5 px-3 py-2 border rounded-md"
        />
        <input
          v-model.number="item.qty"
          type="number"
          class="col-span-2 px-3 py-2 border rounded-md"
          min="1"
        />
        <input
          v-model.number="item.rate"
          type="number"
          class="col-span-2 px-3 py-2 border rounded-md"
          min="0"
        />
        <input
          :value="formatCurrency(item.qty * item.rate)"
          disabled
          class="col-span-2 px-3 py-2 border rounded-md bg-gray-100 text-right"
        />
        <button @click="removeItem(index)" class="col-span-1 text-red-500 hover:text-red-700">
          <X class="w-5 h-5" />
        </button>
      </div>

      <hr class="my-4" />

      <!-- Totals -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div></div>
        <div class="space-y-2 text-right">
          <div class="flex justify-between">
            <span>Subtotal:</span>
            <span>{{ formatCurrency(subtotal) }}</span>
          </div>
          <div class="flex justify-between items-center gap-2">
            <span>Discount:</span>
            <input v-model.number="discount" type="number" class="w-16 px-2 py-1 border rounded-md" />
            <span>% {{ formatCurrency(discountAmount) }}</span>
          </div>
          <div class="flex justify-between items-center gap-2">
            <span>Tax:</span>
            <input v-model.number="tax" type="number" class="w-16 px-2 py-1 border rounded-md" />
            <span>% {{ formatCurrency(taxAmount) }}</span>
          </div>
          <hr />
          <div class="flex justify-between text-lg font-semibold">
            <span>Total:</span>
            <span>{{ formatCurrency(total) }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Additional Information Section -->
    <div class="bg-white rounded-xl shadow-sm p-6 mt-6">
      <h2 class="text-xl font-semibold text-gray-800 mb-6">Additional Information</h2>

      <div class="mb-4">
        <label class="block text-sm font-medium text-gray-700 mb-1">Notes</label>
        <textarea
          v-model="notes"
          rows="3"
          placeholder="Additional notes for the client"
          class="w-full rounded-md border-gray-300 focus:ring-2 focus:ring-blue-500"
        ></textarea>
      </div>

      <div>
        <label class="block text-sm font-medium text-gray-700 mb-1">Terms & Conditions</label>
        <textarea
          v-model="terms"
          rows="3"
          placeholder="Enter terms & conditions"
          class="w-full rounded-md border-gray-300 focus:ring-2 focus:ring-blue-500"
        ></textarea>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { Eye, Save, Plus, X } from 'lucide-vue-next'

const quoteNumber = `QUO-${Date.now()}`
const currentDate = new Date().toISOString().split('T')[0]

const items = ref([{ description: '', qty: 1, rate: 0 }])
const discount = ref(0)
const tax = ref(18)

const subtotal = computed(() =>
  items.value.reduce((sum, item) => sum + item.qty * item.rate, 0)
)
const discountAmount = computed(() =>
  (subtotal.value * discount.value) / 100
)
const taxAmount = computed(() =>
  ((subtotal.value - discountAmount.value) * tax.value) / 100
)
const total = computed(() =>
  subtotal.value - discountAmount.value + taxAmount.value
)

const addItem = () => {
  items.value.push({ description: '', qty: 1, rate: 0 })
}
const removeItem = (index) => {
  items.value.splice(index, 1)
}

const formatCurrency = (value) =>
  new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    minimumFractionDigits: 2,
  }).format(value)

const notes = ref('')
const terms = ref('Quote valid for 30 days')
</script>
