const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY
);

// Get user profile
const getUserProfile = async (req, res) => {
  try {
    const userId = req.user.id;

    // Get user data from Supabase Auth
    const { data: authUser, error: authError } = await supabase.auth.admin.getUserById(userId);
    
    if (authError) {
      return res.status(400).json({ 
        success: false, 
        message: 'Failed to fetch user data',
        error: authError.message 
      });
    }

    // For now, just return the auth user data since we don't have profiles table set up
    // In a production environment, you would set up the profiles table in Supabase
    const profileData = {
      id: userId,
      email: authUser.user.email,
      full_name: authUser.user.user_metadata?.full_name || '',
      avatar_url: authUser.user.user_metadata?.avatar_url || '',
      phone: authUser.user.user_metadata?.phone || '',
      company: '',
      address: '',
      city: '',
      state: '',
      postal_code: '',
      country: '',
      timezone: 'UTC',
      currency: 'USD',
      language: 'en',
      created_at: authUser.user.created_at,
      updated_at: authUser.user.updated_at || authUser.user.created_at
    }

    res.json({
      success: true,
      data: profileData
    });

  } catch (error) {
    console.error('Error in getUserProfile:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error',
      error: error.message 
    });
  }
};

// Update user profile
const updateUserProfile = async (req, res) => {
  try {
    const userId = req.user.id;
    const {
      full_name,
      phone
    } = req.body;

    // For now, we'll just update the auth metadata
    // In production, you would also update a profiles table
    const updateData = {};
    if (full_name) updateData.full_name = full_name;
    if (phone) updateData.phone = phone;

    if (Object.keys(updateData).length > 0) {
      const { data, error } = await supabase.auth.admin.updateUserById(userId, {
        user_metadata: updateData
      });

      if (error) {
        return res.status(400).json({
          success: false,
          message: 'Failed to update profile',
          error: error.message
        });
      }
    }

    // Return updated profile data
    const { data: authUser, error: authError } = await supabase.auth.admin.getUserById(userId);

    if (authError) {
      return res.status(400).json({
        success: false,
        message: 'Failed to fetch updated user data',
        error: authError.message
      });
    }

    const profileData = {
      id: userId,
      email: authUser.user.email,
      full_name: authUser.user.user_metadata?.full_name || '',
      avatar_url: authUser.user.user_metadata?.avatar_url || '',
      phone: authUser.user.user_metadata?.phone || '',
      company: req.body.company || '',
      address: req.body.address || '',
      city: req.body.city || '',
      state: req.body.state || '',
      postal_code: req.body.postal_code || '',
      country: req.body.country || '',
      timezone: req.body.timezone || 'UTC',
      currency: req.body.currency || 'USD',
      language: req.body.language || 'en',
      created_at: authUser.user.created_at,
      updated_at: new Date().toISOString()
    };

    res.json({
      success: true,
      message: 'Profile updated successfully',
      data: profileData
    });

  } catch (error) {
    console.error('Error in updateUserProfile:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
};

// Update user password
const updatePassword = async (req, res) => {
  try {
    const userId = req.user.id;
    const { currentPassword, newPassword } = req.body;

    if (!currentPassword || !newPassword) {
      return res.status(400).json({
        success: false,
        message: 'Current password and new password are required'
      });
    }

    if (newPassword.length < 6) {
      return res.status(400).json({
        success: false,
        message: 'New password must be at least 6 characters long'
      });
    }

    // Update password using Supabase Admin API
    const { data, error } = await supabase.auth.admin.updateUserById(userId, {
      password: newPassword
    });

    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Failed to update password',
        error: error.message
      });
    }

    res.json({
      success: true,
      message: 'Password updated successfully'
    });

  } catch (error) {
    console.error('Error in updatePassword:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
};

// Get user preferences/settings
const getUserSettings = async (req, res) => {
  try {
    const userId = req.user.id;

    // Return default settings for now
    // In production, you would store these in a user_settings table
    const defaultSettings = {
      user_id: userId,
      notifications_email: true,
      notifications_browser: true,
      auto_save_drafts: true,
      default_currency: 'USD',
      default_tax_rate: 18,
      invoice_terms: 'Payment is due within 30 days',
      company_logo: '',
      invoice_template: 'default',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    res.json({
      success: true,
      data: defaultSettings
    });

  } catch (error) {
    console.error('Error in getUserSettings:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
};

// Update user settings
const updateUserSettings = async (req, res) => {
  try {
    const userId = req.user.id;
    const settingsData = req.body;

    // For now, just return the updated settings
    // In production, you would store these in a user_settings table
    const updatedSettings = {
      user_id: userId,
      ...settingsData,
      updated_at: new Date().toISOString()
    };

    res.json({
      success: true,
      message: 'Settings updated successfully',
      data: updatedSettings
    });

  } catch (error) {
    console.error('Error in updateUserSettings:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
};

module.exports = {
  getUserProfile,
  updateUserProfile,
  updatePassword,
  getUserSettings,
  updateUserSettings
};
