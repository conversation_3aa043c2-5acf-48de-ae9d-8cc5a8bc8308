<template>
    <div class="h-full font-serif p-20 flex flex-col items-center justify-center gap-y-2">
      <div class="h-48 w-48">
        <img class="rounded-lg mx-auto" src="/assets/empty.gif" alt="empty" />
      </div>
      <p class="text-muted-foreground text-sm text-center">
        {{ label }}
      </p>
    </div>
  </template>
  
  <script lang="ts" setup>
  defineProps<{
    label: string;
  }>();
  </script>
  