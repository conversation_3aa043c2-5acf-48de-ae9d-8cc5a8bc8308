<template>
  <div class="space-y-4">
    <!-- Loading State -->
    <div v-if="isLoading" class="flex items-center justify-center py-8">
      <Icon name="lucide:loader-2" class="w-6 h-6 animate-spin text-[#00C951]" />
      <span class="ml-2 text-gray-600">Loading invoices...</span>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="text-center py-8">
      <Icon name="lucide:alert-circle" class="w-8 h-8 mx-auto mb-2 text-red-500" />
      <p class="text-red-600 mb-4">{{ error }}</p>
      <Button @click="loadClientInvoices" variant="outline" size="sm">
        <Icon name="lucide:refresh-cw" class="w-4 h-4 mr-2" />
        Retry
      </Button>
    </div>

    <!-- Empty State -->
    <div v-else-if="clientInvoices.length === 0" class="text-center py-8">
      <Icon name="lucide:file-text" class="w-12 h-12 mx-auto mb-4 text-gray-400" />
      <h3 class="text-lg font-medium text-gray-900 mb-2">No Invoices Found</h3>
      <p class="text-gray-600">This client doesn't have any invoices yet.</p>
    </div>

    <!-- Invoice List -->
    <div v-else class="space-y-4">
      <!-- Summary -->
      <div class="bg-gray-50 rounded-lg p-4">
        <div class="grid grid-cols-1 sm:grid-cols-3 gap-4 text-center">
          <div>
            <p class="text-sm text-gray-600">Total Invoices</p>
            <p class="text-lg font-semibold text-gray-900">{{ clientInvoices.length }}</p>
          </div>
          <div>
            <p class="text-sm text-gray-600">Total Amount</p>
            <p class="text-lg font-semibold text-gray-900">₹{{ formatCurrency(totalAmount) }}</p>
          </div>
          <div>
            <p class="text-sm text-gray-600">Outstanding</p>
            <p class="text-lg font-semibold text-gray-900">₹{{ formatCurrency(outstandingAmount) }}</p>
          </div>
        </div>
      </div>

      <!-- Invoice Table -->
      <div class="overflow-x-auto rounded-lg border">
        <table class="min-w-full text-sm text-left">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-4 py-3 font-medium text-gray-700">Invoice #</th>
              <th class="px-4 py-3 font-medium text-gray-700">Date</th>
              <th class="px-4 py-3 font-medium text-gray-700">Due Date</th>
              <th class="px-4 py-3 font-medium text-gray-700">Amount</th>
              <th class="px-4 py-3 font-medium text-gray-700">Status</th>
              <th class="px-4 py-3 font-medium text-gray-700">Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="invoice in clientInvoices"
              :key="invoice._id"
              class="border-t hover:bg-gray-50 cursor-pointer"
              @click="viewInvoice(invoice)"
            >
              <td class="px-4 py-3 font-medium text-gray-900">{{ invoice.invoiceNumber }}</td>
              <td class="px-4 py-3 text-gray-600">{{ formatDate(invoice.issueDate || invoice.createdAt) }}</td>
              <td class="px-4 py-3 text-gray-600">{{ formatDate(invoice.dueDate) }}</td>
              <td class="px-4 py-3 font-medium text-gray-900">₹{{ formatCurrency(invoice.total) }}</td>
              <td class="px-4 py-3">
                <div class="flex items-center space-x-2">
                  <span
                    :class="[
                      'inline-block px-2 py-1 rounded-full text-xs font-semibold capitalize',
                      getStatusClass(getDisplayStatus(invoice))
                    ]"
                  >
                    {{ getDisplayStatus(invoice) }}
                  </span>
                  <span v-if="isOverdue(invoice)" class="inline-block px-2 py-1 rounded-full text-xs font-semibold bg-red-100 text-red-800">
                    Overdue
                  </span>
                </div>
              </td>
              <td class="px-4 py-3">
                <div class="flex items-center space-x-2">
                  <Button variant="ghost" size="sm" @click.stop="viewInvoice(invoice)">
                    <Icon name="lucide:eye" class="w-4 h-4" />
                  </Button>
                  <Button variant="ghost" size="sm" @click.stop="downloadPDF(invoice)">
                    <Icon name="lucide:download" class="w-4 h-4" />
                  </Button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div v-if="pagination.totalPages > 1" class="flex items-center justify-between">
        <p class="text-sm text-gray-600">
          Showing {{ ((pagination.page - 1) * pagination.limit) + 1 }} to 
          {{ Math.min(pagination.page * pagination.limit, pagination.totalInvoices) }} of 
          {{ pagination.totalInvoices }} invoices
        </p>
        <div class="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            :disabled="!pagination.hasPrevPage"
            @click="changePage(pagination.page - 1)"
          >
            Previous
          </Button>
          <span class="text-sm text-gray-600">
            Page {{ pagination.page }} of {{ pagination.totalPages }}
          </span>
          <Button
            variant="outline"
            size="sm"
            :disabled="!pagination.hasNextPage"
            @click="changePage(pagination.page + 1)"
          >
            Next
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useInvoicesStore } from '~/stores/invoices'
import type { Invoice } from '~/services/invoiceApi'

interface Props {
  clientName: string
}

const props = defineProps<Props>()
const router = useRouter()
const invoicesStore = useInvoicesStore()

const isLoading = ref(false)
const error = ref('')
const clientInvoices = ref<Invoice[]>([])
const pagination = ref({
  page: 1,
  limit: 10,
  totalPages: 0,
  totalInvoices: 0,
  hasNextPage: false,
  hasPrevPage: false
})

// Computed properties
const totalAmount = computed(() => {
  return clientInvoices.value.reduce((sum: number, invoice: Invoice) => sum + (invoice.total || 0), 0)
})

const outstandingAmount = computed(() => {
  return clientInvoices.value
    .filter((invoice: Invoice) => !invoice.isPaid)
    .reduce((sum: number, invoice: Invoice) => sum + (invoice.total || 0), 0)
})

// Methods
const loadClientInvoices = async (page = 1) => {
  try {
    isLoading.value = true
    error.value = ''

    console.log('🔍 Loading invoices for client:', props.clientName)

    const response = await invoicesStore.fetchClientInvoices(props.clientName, {
      page,
      limit: pagination.value.limit,
      sort: '-createdAt'
    })

    clientInvoices.value = response.data
    pagination.value = response.pagination

    console.log('✅ Client invoices loaded:', {
      client: props.clientName,
      count: clientInvoices.value.length,
      total: totalAmount.value,
      outstanding: outstandingAmount.value
    })

  } catch (err: any) {
    error.value = err.message || 'Failed to load client invoices'
    console.error('❌ Error loading client invoices:', err)
  } finally {
    isLoading.value = false
  }
}

const changePage = async (page: number) => {
  await loadClientInvoices(page)
}

const viewInvoice = (invoice: Invoice) => {
  console.log('🔍 Viewing invoice:', invoice.invoiceNumber)
  router.push(`/invoices/${invoice._id}`)
}

const downloadPDF = async (invoice: Invoice) => {
  try {
    console.log('📄 Downloading PDF for invoice:', invoice.invoiceNumber)
    // This would integrate with the existing PDF download functionality
    // For now, just navigate to the invoice view page
    router.push(`/invoices/${invoice._id}`)
  } catch (err: any) {
    console.error('❌ Error downloading PDF:', err)
  }
}

// Helper functions
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-IN').format(amount || 0)
}

const formatDate = (date: string | undefined) => {
  if (!date) return 'N/A'
  return new Date(date).toLocaleDateString('en-IN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

const getDisplayStatus = (invoice: Invoice) => {
  if (invoice.isPaid) return 'paid'
  if (isOverdue(invoice)) return 'overdue'
  return invoice.status || 'draft'
}

const isOverdue = (invoice: Invoice) => {
  if (invoice.isPaid) return false
  const dueDate = new Date(invoice.dueDate)
  const today = new Date()
  today.setHours(0, 0, 0, 0)
  return dueDate < today
}

const getStatusClass = (status: string) => {
  switch (status) {
    case 'paid':
      return 'bg-green-100 text-green-800'
    case 'sent':
      return 'bg-blue-100 text-blue-800'
    case 'draft':
      return 'bg-gray-100 text-gray-800'
    case 'overdue':
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

// Watch for client name changes and reload invoices
watch(() => props.clientName, (newClientName: string) => {
  if (newClientName) {
    loadClientInvoices()
  }
}, { immediate: true })

// Load invoices on mount
onMounted(() => {
  if (props.clientName) {
    loadClientInvoices()
  }
})

// Auto-refresh invoices every 30 seconds for real-time updates
let refreshInterval: NodeJS.Timeout | null = null

const startAutoRefresh = () => {
  if (refreshInterval) clearInterval(refreshInterval)
  refreshInterval = setInterval(() => {
    if (props.clientName && !isLoading.value) {
      loadClientInvoices(pagination.value.page)
    }
  }, 30000) // 30 seconds
}

const stopAutoRefresh = () => {
  if (refreshInterval) {
    clearInterval(refreshInterval)
    refreshInterval = null
  }
}

// Start auto-refresh when component mounts
onMounted(() => {
  startAutoRefresh()
})

// Clean up interval when component unmounts
onUnmounted(() => {
  stopAutoRefresh()
})
</script>
