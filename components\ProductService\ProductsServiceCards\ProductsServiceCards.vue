<template>
  <div class="bg-white text-black p-4 space-y-6">
    <!-- Loading State -->
    <div v-if="isLoading" class="flex justify-center items-center py-8">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-[#00C951]"></div>
      <span class="ml-2 text-gray-600">Loading products & services...</span>
    </div>

    <!-- Error State -->
    <div v-else-if="hasError" class="bg-red-50 border border-red-200 rounded-lg p-4">
      <div class="flex items-center">
        <Icon name="lucide:alert-circle" class="w-5 h-5 text-red-500 mr-2"/>
        <span class="text-red-700">Failed to load data</span>
      </div>
      <button
        @click="refreshData"
        class="mt-2 text-sm text-red-600 hover:text-red-800 underline"
      >
        Try again
      </button>
    </div>

    <!-- Content -->
    <div v-else>
      <!-- Top Cards -->
      <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
        <!-- Total Items Card -->
        <div>
          <Itemstotal/>
        </div>

        <!-- Products Card -->
        <div>
          <ProductTotal/>
        </div>

        <!-- Service Card -->
        <div>
          <ServiceTotal/>
        </div>
      </div>

      <!-- Statistics Cards -->
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mt-6">

        <!-- Product Value -->
        <div class="bg-white text-black rounded-2xl p-4 shadow-sm border border-gray-200 hover:shadow-lg transition-shadow duration-200">
          <div class="flex justify-between items-center">
            <h3 class="text-sm text-gray-600 font-semibold">Product Value</h3>
            <Icon name="lucide:package" class="w-5 h-5 text-[#05DF72]"/>
          </div>
          <div class="text-2xl font-bold mt-2 text-blue-600">
            ${{ formatCurrency(productsStore.stats?.totalValue || 0) }}
          </div>
          <div class="text-sm text-gray-500 mt-1">
            Inventory value
          </div>
        </div>

        <!-- Service Revenue -->
        <div class="bg-white text-black rounded-2xl p-4 shadow-sm border border-gray-200 hover:shadow-lg transition-shadow duration-200">
          <div class="flex justify-between items-center">
            <h3 class="text-sm text-gray-600 font-semibold">Service Revenue</h3>
            <Icon name="lucide:trending-up" class="w-5 h-5 text-[#05DF72]"/>
          </div>
          <div class="text-2xl font-bold mt-2 text-purple-600">
            ${{ formatCurrency(servicesStore.stats?.totalRevenue || 0) }}
          </div>
          <div class="text-sm text-gray-500 mt-1">
            Total earned
          </div>
        </div>

        <!-- Low Stock Alert -->
        <div class="bg-white text-black rounded-2xl p-4 shadow-sm border border-gray-200 hover:shadow-lg transition-shadow duration-200">
          <div class="flex justify-between items-center">
            <h3 class="text-sm text-gray-600 font-semibold">Low Stock</h3>
            <Icon name="lucide:alert-triangle" class="w-5 h-5 text-orange-500"/>
          </div>
          <div class="text-2xl font-bold mt-2 text-orange-600">
            {{ productsStore.stats?.lowStockProducts || 0 }}
          </div>
          <div class="text-sm text-gray-500 mt-1">
            Products need restock
          </div>
        </div>

        <!-- Average Service Price -->
        <div class="bg-white text-black rounded-2xl p-4 shadow-sm border border-gray-200 hover:shadow-lg transition-shadow duration-200">
          <div class="flex justify-between items-center">
            <h3 class="text-sm text-gray-600 font-semibold">Avg Service Price</h3>
            <Icon name="lucide:dollar-sign" class="w-5 h-5 text-[#05DF72]"/>
          </div>
          <div class="text-2xl font-bold mt-2 text-green-600">
            ${{ formatCurrency(servicesStore.stats?.averagePrice || 0) }}
          </div>
          <div class="text-sm text-gray-500 mt-1">
            Average pricing
          </div>
        </div>

      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useProductsStore } from '~/stores/products'
import { useServicesStore } from '~/stores/services'

const productsStore = useProductsStore()
const servicesStore = useServicesStore()

// Computed properties for loading and error states
const isLoading = computed(() => productsStore.isLoading || servicesStore.isLoading)
const hasError = computed(() => productsStore.hasError || servicesStore.hasError)

// Fetch data on component mount
onMounted(async () => {
  try {
    await Promise.all([
      productsStore.fetchProductStats(),
      servicesStore.fetchServiceStats()
    ])
  } catch (error) {
    console.error('Failed to load products & services data:', error)
  }
})

// Refresh all data
const refreshData = async () => {
  try {
    await Promise.all([
      productsStore.fetchProductStats(),
      servicesStore.fetchServiceStats()
    ])
  } catch (error) {
    console.error('Failed to refresh products & services data:', error)
  }
}

// Helper function to format currency
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount)
}
</script>
  