<template>
  <div class="min-h-screen bg-background p-6">
    <div class="max-w-6xl mx-auto bg-card rounded-lg shadow-sm p-8">
      <h1 class="text-heading font-heading text-foreground mb-8">Service Billing</h1>

      <form @submit.prevent="handleSubmit" class="space-y-8">
        <!-- Invoice Details -->
        <section class="bg-secondary p-6 rounded-lg">
          <h2 class="text-xl font-semibold mb-4">Invoice Details</h2>
          <div class="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
            <div class="flex items-center">
              <Icon name="lucide:info" class="w-4 h-4 text-blue-600 mr-2" />
              <span class="text-sm text-blue-700">Invoice number will be auto-generated when you create the invoice</span>
            </div>
          </div>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium mb-1">Issue Date</label>
              <input type="date" v-model="invoiceDate" class="border border-gray-300 rounded w-full px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent" />
            </div>
            <div>
              <label class="block text-sm font-medium mb-1">Due Date</label>
              <input type="date" v-model="dueDate" class="border border-gray-300 rounded w-full px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent" />
            </div>
            <div class="md:col-span-2">
              <label class="block text-sm font-medium mb-1">Select Client</label>
              <select
                v-model="selectedClient"
                @change="selectClient($event.target.value)"
                class="border border-gray-300 rounded w-full px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent"
                :disabled="isLoadingClients"
              >
                <option value="">{{ isLoadingClients ? 'Loading clients...' : 'Choose a client' }}</option>
                <option v-for="client in clients" :key="client._id" :value="client._id">
                  {{ client.name }} ({{ client.email }})
                </option>
              </select>
            </div>
          </div>
        </section>

        <!-- Client Information Display -->
        <section v-if="selectedClientData" class="bg-gray-50 p-6 rounded-lg">
          <h3 class="text-lg font-semibold mb-3">Client Details</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <span class="font-medium">Name:</span> {{ selectedClientData.name }}
            </div>
            <div>
              <span class="font-medium">Email:</span> {{ selectedClientData.email }}
            </div>
            <div v-if="selectedClientData.phone">
              <span class="font-medium">Phone:</span> {{ selectedClientData.phone }}
            </div>
            <div v-if="selectedClientData.address">
              <span class="font-medium">Address:</span>
              {{ formatAddress(selectedClientData.address) }}
            </div>
          </div>
        </section>

        <!-- Services -->
        <section class="bg-secondary p-6 rounded-lg">
          <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-semibold">Services</h2>
            <Button
              @click="addService"
              variant="default"
              class="gap-2 hover:bg-green-700 transition-colors duration-200"
              title="Add a new service row"
            >
              <Plus class="w-4 h-4" />
              Add Service
            </Button>
          </div>

          <div
            v-for="(service, index) in services"
            :key="service.id"
            class="grid grid-cols-1 md:grid-cols-5 gap-4 mb-4 p-4 bg-card rounded-sm border"
          >
            <div class="md:col-span-2">
              <div class="flex items-center justify-between mb-2">
                <label class="block text-sm font-medium text-gray-700">
                  Service
                  <span class="text-green-600 font-normal">(Manual entry always available)</span>
                </label>
                <button
                  v-if="service.serviceId"
                  @click="clearServiceSelection(index)"
                  class="text-xs text-blue-600 hover:text-blue-800 underline px-2 py-1 rounded hover:bg-blue-50 transition-colors"
                  type="button"
                  title="Clear selection and enter manually"
                >
                  <Icon name="lucide:edit-3" class="w-3 h-3 inline mr-1" />
                  Manual Entry
                </button>
              </div>

              <select
                v-if="availableServices.length > 0"
                :value="service.serviceId"
                @change="selectService(index, $event.target.value)"
                class="w-full border border-gray-300 rounded px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent mb-2"
              >
                <option value="">🖊️ Enter manually or select from services below</option>
                <option v-for="availableService in availableServices" :key="availableService._id" :value="availableService._id">
                  {{ availableService.name }} - Rs.{{ availableService.price }}
                </option>
              </select>

              <div v-else class="text-xs text-gray-500 italic bg-blue-50 p-3 rounded border border-blue-200 mb-2">
                <Icon name="lucide:info" class="w-3 h-3 inline mr-1" />
                No services in inventory - enter service details manually below
              </div>

              <input
                v-model="service.description"
                placeholder="Service description (e.g., 'Web Development - Custom E-commerce Platform')"
                class="w-full border border-gray-300 rounded px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                :class="{ 'border-red-300 bg-red-50': !service.description.trim() }"
              />

              <!-- Manual Entry Indicator -->
              <div v-if="!service.serviceId && service.description" class="text-xs text-green-600 mt-2 flex items-center bg-green-50 p-2 rounded border border-green-200">
                <Icon name="lucide:check-circle" class="w-3 h-3 mr-1" />
                Manual entry mode - You're creating a custom service
              </div>

              <!-- Validation Message -->
              <div v-if="!service.description.trim()" class="text-xs text-red-500 mt-1 flex items-center">
                <Icon name="lucide:alert-circle" class="w-3 h-3 mr-1" />
                Service description is required
              </div>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Quantity</label>
              <input
                type="number"
                v-model.number="service.quantity"
                min="1"
                step="1"
                class="w-full border border-gray-300 rounded px-3 py-2 text-sm text-center focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                :class="{ 'border-red-300 bg-red-50': (service.quantity || 0) <= 0 }"
                @input="service.quantity = Number($event.target.value) || 1"
              />
              <span class="text-xs text-gray-500 mt-1">Qty: {{ service.quantity }}</span>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Unit Price</label>
              <input
                type="number"
                v-model.number="service.unitPrice"
                min="0"
                step="0.01"
                class="w-full border border-gray-300 rounded px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                :class="{ 'border-red-300 bg-red-50': (service.unitPrice || 0) <= 0 }"
                @input="service.unitPrice = Number($event.target.value) || 0"
              />
              <span class="text-xs text-blue-600 mt-1">Rs.{{ (service.unitPrice || 0).toFixed(2) }}</span>
            </div>
            <div class="flex items-end justify-between">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Total</label>
                <div class="text-sm font-semibold text-gray-900 bg-gray-50 px-3 py-2 rounded border">
                  Rs.{{ ((service.unitPrice || 0) * (service.quantity || 0)).toFixed(2) }}
                </div>
                <span class="text-xs text-gray-500 mt-1">Amount</span>
              </div>
              <div class="flex flex-col items-center space-y-2">
                <Button
                  type="button"
                  variant="ghost"
                  class="text-destructive p-2 hover:bg-red-50 hover:text-red-700 transition-colors duration-200 border border-red-200 rounded-full"
                  @click="removeService(service.id)"
                  aria-label="Remove Service"
                  title="Remove this service"
                  :disabled="services.length <= 1"
                  :class="{ 'opacity-50 cursor-not-allowed': services.length <= 1 }"
                >
                  <Trash2 class="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>
        </section>

        <!-- Calculations -->
        <section class="bg-gray-50 p-6 rounded-lg">
          <h2 class="text-xl font-semibold mb-4">Summary</h2>
          <div class="space-y-4">
            <div class="flex justify-between">
              <span>Subtotal:</span>
              <span>Rs.{{ subtotal.toFixed(2) }}</span>
            </div>
            <div class="flex justify-between items-center gap-4 py-2">
              <span class="text-sm font-medium text-gray-700 min-w-[80px]">Discount:</span>
              <div class="flex items-center gap-3">
                <input
                  type="number"
                  v-model.number="discountRate"
                  min="0"
                  max="100"
                  step="0.01"
                  class="border border-gray-300 rounded-md px-3 py-2 w-16 text-sm text-center focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="0"
                />
                <span class="text-xs text-gray-500 min-w-[12px]">%</span>
                <span class="text-sm font-semibold text-orange-600 min-w-[80px] text-right">Rs.{{ discountAmount.toFixed(2) }}</span>
              </div>
            </div>
            <div class="flex justify-between items-center gap-4 py-2">
              <span class="text-sm font-medium text-gray-700 min-w-[80px]">Tax:</span>
              <div class="flex items-center gap-3">
                <input
                  type="number"
                  v-model.number="taxRate"
                  min="0"
                  max="100"
                  step="0.01"
                  class="border border-gray-300 rounded-md px-3 py-2 w-16 text-sm text-center focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="18"
                />
                <span class="text-xs text-gray-500 min-w-[12px]">%</span>
                <span class="text-sm font-semibold text-blue-600 min-w-[80px] text-right">Rs.{{ taxAmount.toFixed(2) }}</span>
              </div>
            </div>
            <div class="flex justify-between text-xl font-bold pt-4 border-t border-gray-300">
              <span>Total:</span>
              <span>Rs.{{ total.toFixed(2) }}</span>
            </div>
          </div>
        </section>

        <!-- Notes and Payment Terms -->
        <section class="bg-secondary p-6 rounded-lg">
          <h2 class="text-xl font-semibold mb-4">Additional Information</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium mb-1">Payment Terms</label>
              <input
                v-model="paymentTerms"
                placeholder="e.g., Net 30, Due on receipt"
                class="w-full border border-gray-300 rounded px-3 py-2 text-sm"
              />
            </div>
            <div>
              <label class="block text-sm font-medium mb-1">Notes</label>
              <textarea
                v-model="notes"
                rows="3"
                placeholder="Additional notes or terms"
                class="w-full border border-gray-300 rounded px-3 py-2 text-sm"
              ></textarea>
            </div>
          </div>
        </section>

        <!-- Actions -->
        <div class="flex flex-wrap gap-4 justify-end">
          <Button
            @click="router.push('/invoices')"
            variant="outline"
            class="gap-2 hover:bg-gray-50 transition-colors duration-200"
            title="Cancel and return to invoice list"
            :disabled="isSubmitting"
          >
            Cancel
          </Button>
          <Button
            @click="saveDraft"
            variant="outline"
            class="gap-2 hover:bg-gray-50 transition-colors duration-200"
            title="Save invoice as draft"
            :disabled="isSubmitting"
          >
            <Save class="w-4 h-4" />
            {{ isSubmitting ? 'Saving...' : 'Save Draft' }}
          </Button>
          <Button
            @click="createInvoice"
            variant="default"
            class="gap-2 hover:bg-green-700 transition-colors duration-200"
            title="Create and send invoice"
            :disabled="isSubmitting"
          >
            <Send class="w-4 h-4" />
            {{ isSubmitting ? 'Creating...' : 'Create Invoice' }}
          </Button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useToast } from 'vue-toastification'
import { nanoid } from 'nanoid'

import { Plus, Trash2, Send, Save } from 'lucide-vue-next'
import { Button } from '@/components/ui/button'
import { useInvoicesStore } from '~/stores/invoices'
import { useClientStore } from '~/stores/clients'
import { useServicesStore } from '~/stores/services'

const router = useRouter()
const toast = useToast()
const invoicesStore = useInvoicesStore()
const clientStore = useClientStore()
const servicesStore = useServicesStore()

// Form data
const invoiceDate = ref(new Date().toISOString().substring(0, 10))
const dueDate = ref('')
const selectedClient = ref('')
const selectedClientData = ref(null)
const notes = ref('')
const paymentTerms = ref('')

const services = reactive([
  { id: nanoid(), description: '', unitPrice: 0, quantity: 1, serviceId: '' }
])

const taxRate = ref(18)
const discountRate = ref(0)
const errors = reactive({})

// Loading states
const isSubmitting = ref(false)
const isLoadingClients = ref(false)
const isLoadingServices = ref(false)

// Computed values
const subtotal = computed(() =>
  services.reduce((sum, item) => sum + (item.unitPrice * item.quantity), 0)
)

const discountAmount = computed(() =>
  (subtotal.value * discountRate.value) / 100
)

const taxAmount = computed(() =>
  ((subtotal.value - discountAmount.value) * taxRate.value) / 100
)

const total = computed(() =>
  subtotal.value - discountAmount.value + taxAmount.value
)

// Get clients and services from stores
const clients = computed(() => clientStore.clients || [])
const availableServices = computed(() => servicesStore.services || [])

function addService() {
  services.push({
    id: nanoid(),
    description: '',
    unitPrice: 0,
    quantity: 1,
    serviceId: ''
  })
}

function removeService(id) {
  const index = services.findIndex(s => s.id === id)
  if (index > -1 && services.length > 1) {
    services.splice(index, 1)
  }
}

const selectClient = (clientId) => {
  const client = clients.value.find(c => c._id === clientId)
  if (client) {
    selectedClientData.value = client
  }
}

const formatAddress = (address) => {
  if (!address) return ''
  const parts = [
    address.street,
    address.city,
    address.state,
    address.zipCode,
    address.country
  ].filter(Boolean)
  return parts.join(', ')
}

const selectService = (index, serviceId) => {
  if (!serviceId) {
    // Clear service selection - allow manual entry
    services[index].serviceId = ''
    toast.info('Service selection cleared. You can now enter custom service details manually.')
    return
  }

  const service = availableServices.value.find(s => s._id === serviceId)
  if (service) {
    services[index].serviceId = serviceId
    services[index].description = service.name + (service.description ? ` - ${service.description}` : '')
    services[index].unitPrice = service.price || 0

    // Show feedback that service was selected but can still be customized
    toast.success(`Service "${service.name}" selected. You can still customize the description and price as needed.`)
  }
}

// Enhanced function to clear service selection
const clearServiceSelection = (index) => {
  services[index].serviceId = ''
  toast.info('Service selection cleared. Enter details manually in the description field.')
}

// Function to validate manual entries
const validateManualEntry = (service) => {
  return service.description.trim() && service.quantity > 0 && service.unitPrice >= 0
}

// Form validation
function validateForm() {
  Object.keys(errors).forEach(key => delete errors[key])

  if (!selectedClientData.value) {
    toast.error('Please select a client')
    return false
  }

  if (!dueDate.value) {
    toast.error('Please select a due date')
    return false
  }

  if (services.length === 0) {
    toast.error('Please add at least one service')
    return false
  }

  for (let i = 0; i < services.length; i++) {
    const service = services[i]
    if (!service.description.trim()) {
      toast.error(`Please enter description for service ${i + 1}`)
      return false
    }
    if (service.quantity <= 0) {
      toast.error(`Please enter valid quantity for service ${i + 1}`)
      return false
    }
    if (service.unitPrice <= 0) {
      toast.error(`Please enter valid unit price for service ${i + 1}`)
      return false
    }
  }

  return true
}

// Submit invoice
const submitInvoice = async (status = 'draft') => {
  if (!validateForm()) return

  try {
    isSubmitting.value = true

    const invoiceData = {
      clientName: selectedClientData.value.name,
      clientEmail: selectedClientData.value.email,
      clientPhone: selectedClientData.value.phone || '',
      clientAddress: selectedClientData.value.address || {},
      items: services.map(service => ({
        description: service.description,
        quantity: service.quantity,
        unitPrice: service.unitPrice,
        type: 'service',
        serviceId: service.serviceId || undefined
      })),
      taxRate: taxRate.value,
      discountRate: discountRate.value,
      dueDate: dueDate.value,
      issueDate: invoiceDate.value,
      notes: notes.value,
      paymentTerms: paymentTerms.value,
      status: status,
      invoiceType: 'service'
    }

    await invoicesStore.createInvoice(invoiceData)

    toast.success(`Invoice ${status === 'draft' ? 'saved as draft' : 'created'} successfully!`)

    // Redirect to invoice list
    router.push('/invoices')

  } catch (error) {
    console.error('Error creating invoice:', error)
    toast.error(error.message || 'Failed to create invoice')
  } finally {
    isSubmitting.value = false
  }
}

const saveDraft = () => submitInvoice('draft')
const createInvoice = () => submitInvoice('sent')

function handleSubmit() {
  createInvoice()
}

// Load data on mount
onMounted(async () => {
  try {
    isLoadingClients.value = true
    isLoadingServices.value = true

    // Load clients and services
    await Promise.all([
      clientStore.fetchClients(),
      servicesStore.fetchServices()
    ])

  } catch (error) {
    console.error('Error loading data:', error)
    toast.error('Failed to load clients and services')
  } finally {
    isLoadingClients.value = false
    isLoadingServices.value = false
  }
})
</script>

<style scoped>
.input {
  width: 5rem;
  border: 1px solid var(--tw-border-color);
  padding: 0.25rem;
  border-radius: 0.25rem;
}
</style>
