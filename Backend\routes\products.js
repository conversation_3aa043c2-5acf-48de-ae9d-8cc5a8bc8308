const express = require('express');
const router = express.Router();
const {
  getProducts,
  getProduct,
  createProduct,
  updateProduct,
  deleteProduct,
  getProductStats,
  getProductCategories,
  updateProductStock
} = require('../controllers/productController');

// Import Supabase authentication middleware
const { verifySupabaseToken } = require('../middleware/supabaseAuth');

// Apply Supabase JWT authentication to all product routes
router.use(verifySupabaseToken);

/**
 * @route   GET /api/products/stats
 * @desc    Get product statistics for authenticated user
 * @access  Private (Supabase JWT required)
 */
router.get('/stats', getProductStats);

/**
 * @route   GET /api/products/categories
 * @desc    Get product categories
 * @access  Private (Supabase JWT required)
 */
router.get('/categories', getProductCategories);

/**
 * @route   GET /api/products
 * @desc    Get all products for authenticated user
 * @access  Private (Supabase JWT required)
 * @query   page, limit, search, status, category, sort, lowStock
 */
router.get('/', getProducts);

/**
 * @route   GET /api/products/:id
 * @desc    Get single product by ID
 * @access  Private (Supabase JWT required)
 */
router.get('/:id', getProduct);

/**
 * @route   POST /api/products
 * @desc    Create new product
 * @access  Private (Supabase JWT required)
 */
router.post('/', createProduct);

/**
 * @route   PUT /api/products/:id
 * @desc    Update product by ID
 * @access  Private (Supabase JWT required)
 */
router.put('/:id', updateProduct);

/**
 * @route   PUT /api/products/:id/stock
 * @desc    Update product stock by ID
 * @access  Private (Supabase JWT required)
 */
router.put('/:id/stock', updateProductStock);

/**
 * @route   DELETE /api/products/:id
 * @desc    Delete product by ID
 * @access  Private (Supabase JWT required)
 */
router.delete('/:id', deleteProduct);

module.exports = router;
