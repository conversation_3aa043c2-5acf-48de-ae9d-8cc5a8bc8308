// plugins/supabase.client.ts
export default defineNuxtPlugin(async () => {
  const supabase = useSupabaseClient()
  
  // Configure Supabase client for better CORS handling
  if (process.client) {
    // Set up auth state listener with better error handling
    supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('🔐 Auth state change:', event)
      
      if (event === 'SIGNED_OUT') {
        // Clear any cached data
        await navigateTo('/Auth/login')
      } else if (event === 'SIGNED_IN') {
        console.log('✅ User signed in:', session?.user?.email)
      } else if (event === 'TOKEN_REFRESHED') {
        console.log('🔄 Token refreshed for:', session?.user?.email)
      }
    })

    // Handle initial session recovery
    try {
      const { data: { session }, error } = await supabase.auth.getSession()
      
      if (error) {
        console.warn('⚠️ Session recovery error:', error.message)
        // Don't throw error, just log it
      }
      
      if (session) {
        console.log('🔄 Session recovered for:', session.user?.email)
      }
    } catch (error) {
      console.warn('⚠️ Failed to recover session:', error)
      // Don't throw error in plugin
    }
  }
})
