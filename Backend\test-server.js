const express = require('express');
const cors = require('cors');
const jwt = require('jsonwebtoken');
require('dotenv').config();

const app = express();

// Middleware
app.use(cors());
app.use(express.json());

// Simple JWT verification middleware
const verifyToken = (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader) {
      return res.status(401).json({
        success: false,
        message: 'No token provided'
      });
    }

    const token = authHeader.startsWith('Bearer ') ? authHeader.slice(7) : authHeader;
    
    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'Invalid token format'
      });
    }

    console.log('🔍 Received token (first 50 chars):', token.substring(0, 50) + '...');
    
    // Decode token to inspect structure
    const decoded = jwt.decode(token, { complete: true });
    console.log('🔍 Token structure:', {
      header: decoded?.header,
      payload: {
        iss: decoded?.payload?.iss,
        aud: decoded?.payload?.aud,
        exp: decoded?.payload?.exp,
        sub: decoded?.payload?.sub,
        email: decoded?.payload?.email
      }
    });

    // Try to verify with JWT secret
    const SUPABASE_JWT_SECRET = process.env.SUPABASE_JWT_SECRET;
    
    if (!SUPABASE_JWT_SECRET) {
      return res.status(500).json({
        success: false,
        message: 'JWT secret not configured'
      });
    }

    try {
      const verified = jwt.verify(token, SUPABASE_JWT_SECRET, {
        algorithms: ['HS256']
      });
      
      console.log('✅ Token verified successfully');
      
      req.user = {
        id: verified.sub,
        email: verified.email,
        role: verified.role || 'authenticated'
      };
      
      next();
    } catch (verifyError) {
      console.error('❌ JWT verification failed:', verifyError.message);
      return res.status(401).json({
        success: false,
        message: 'Token verification failed',
        error: verifyError.message
      });
    }
  } catch (error) {
    console.error('❌ Token processing error:', error.message);
    return res.status(401).json({
      success: false,
      message: 'Token processing failed',
      error: error.message
    });
  }
};

// Routes
app.get('/', (req, res) => {
  res.json({
    message: 'Test server is running',
    timestamp: new Date().toISOString()
  });
});

app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'Test server is healthy',
    timestamp: new Date().toISOString()
  });
});

app.get('/api/debug-token', (req, res) => {
  const authHeader = req.headers.authorization;
  
  if (!authHeader) {
    return res.json({
      success: false,
      message: 'No Authorization header provided'
    });
  }

  const token = authHeader.startsWith('Bearer ') ? authHeader.slice(7) : authHeader;
  
  try {
    const decoded = jwt.decode(token, { complete: true });
    
    res.json({
      success: true,
      message: 'Token debug information',
      tokenInfo: {
        header: decoded?.header,
        payload: {
          iss: decoded?.payload?.iss,
          aud: decoded?.payload?.aud,
          exp: decoded?.payload?.exp,
          iat: decoded?.payload?.iat,
          sub: decoded?.payload?.sub,
          email: decoded?.payload?.email,
          role: decoded?.payload?.role
        },
        timing: {
          issued: new Date(decoded?.payload?.iat * 1000).toISOString(),
          expires: new Date(decoded?.payload?.exp * 1000).toISOString(),
          isExpired: decoded?.payload?.exp < Math.floor(Date.now() / 1000)
        }
      }
    });
  } catch (error) {
    res.json({
      success: false,
      message: 'Failed to decode token',
      error: error.message
    });
  }
});

app.get('/api/test-auth', verifyToken, (req, res) => {
  res.json({
    success: true,
    message: 'Authentication successful!',
    user: req.user,
    timestamp: new Date().toISOString()
  });
});

const PORT = process.env.PORT || 5002;

app.listen(PORT, () => {
  console.log(`🚀 Test server running on port ${PORT}`);
  console.log(`📍 Access at: http://localhost:${PORT}`);
  console.log(`🔑 JWT Secret configured: ${!!process.env.SUPABASE_JWT_SECRET}`);
});
