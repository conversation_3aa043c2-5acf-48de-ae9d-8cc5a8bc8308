<script setup lang="ts">
import { useAuthStore } from '~/stores/auth'
import { useToast } from 'vue-toastification'

const authStore = useAuthStore()
const toast = useToast()

const signOut = async () => {
  try {
    await authStore.logout()
    toast.success('Logged out successfully!')
  } catch (error: any) {
    console.error('Logout error:', error)
    toast.error(error.message || 'Failed to logout. Please try again.')
  }
}
</script>

<template>
  <div>
    <DropdownMenu>
      <DropdownMenuTrigger as-child>
        <Button class="hover:bg-[#00C951] hover:text-white bg-white text-black border border-green-300">
          <Icon name="lucide:circle-user-round" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" class="w-72">
        <DropdownMenuLabel>My Account</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <div class="w-full flex items-center pl-3 p-2 gap-2">
          <Avatar>
            <AvatarFallback class="bg-gray-400 text-white font-bold">D</AvatarFallback>
          </Avatar>
          <div>
            <div class="font-bold text-gray-800">Demo User</div>
            <div class="font-bold text-gray-600 text-sm"><EMAIL></div>
          </div>
        </div>
        <DropdownMenuSeparator />
        <DropdownMenuItem>Profile</DropdownMenuItem>
        <DropdownMenuItem>Billing</DropdownMenuItem>
        <DropdownMenuItem>Team</DropdownMenuItem>
        <DropdownMenuItem>Subscription</DropdownMenuItem>
        <DropdownMenuItem @click="signOut" class="cursor-pointer flex items-center">
          <Icon class="mr-2 h-4 w-4" name="heroicons:arrow-left-on-rectangle" />
          <span class="ml-2">Logout</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  </div>
</template>
