const express = require('express');
const router = express.Router();
const {
  getServices,
  getService,
  createService,
  updateService,
  deleteService,
  getServiceStats,
  getServiceCategories,
  toggleServiceAvailability
} = require('../controllers/serviceController');

// Import Supabase authentication middleware
const { verifySupabaseToken } = require('../middleware/supabaseAuth');

// Apply Supabase JWT authentication to all service routes
router.use(verifySupabaseToken);

/**
 * @route   GET /api/services/stats
 * @desc    Get service statistics for authenticated user
 * @access  Private (Supabase JWT required)
 */
router.get('/stats', getServiceStats);

/**
 * @route   GET /api/services/categories
 * @desc    Get service categories
 * @access  Private (Supabase JWT required)
 */
router.get('/categories', getServiceCategories);

/**
 * @route   GET /api/services
 * @desc    Get all services for authenticated user
 * @access  Private (Supabase JWT required)
 * @query   page, limit, search, status, category, pricingType, available, sort
 */
router.get('/', getServices);

/**
 * @route   GET /api/services/:id
 * @desc    Get single service by ID
 * @access  Private (Supabase JWT required)
 */
router.get('/:id', getService);

/**
 * @route   POST /api/services
 * @desc    Create new service
 * @access  Private (Supabase JWT required)
 */
router.post('/', createService);

/**
 * @route   PUT /api/services/:id
 * @desc    Update service by ID
 * @access  Private (Supabase JWT required)
 */
router.put('/:id', updateService);

/**
 * @route   PUT /api/services/:id/availability
 * @desc    Toggle service availability by ID
 * @access  Private (Supabase JWT required)
 */
router.put('/:id/availability', toggleServiceAvailability);

/**
 * @route   DELETE /api/services/:id
 * @desc    Delete service by ID
 * @access  Private (Supabase JWT required)
 */
router.delete('/:id', deleteService);

module.exports = router;
