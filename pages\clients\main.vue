<template>
  <div class="min-h-screen bg-gray-50">
    <div class="max-w-7xl mx-auto p-6 space-y-8">
    <Heading
      title="Manage Clients"
      description="Manage your clients and their details"
      icon="lucide:users"
      iconColor="text-green-400"
      bgColor="bg-white"
      :buttons="[
        {
          label: 'View All',
          icon: 'lucide:import',
          bgColor: 'bg-gray-200',
          textColor: 'text-black',
          to:'/clients/TotalClients'
        },
        {
          label: 'Import',
          icon: 'lucide:import',
          bgColor: 'bg-gray-200',
          textColor: 'text-black',
          to:'/clients/Clientimport'
        },
        {
          label: 'Reports',
          icon: 'lucide:notepad-text',
          bgColor: 'bg-gray-200',
          textColor: 'text-black',
          to:'/clients/reports'
        },
        {
          label: 'Add Client',
          icon: 'lucide:circle-plus',
          bgColor: 'bg-[#00C951]',
          textColor: 'text-white',
          action: 'openClient'
        }
      ]"
      @button-click="handleHeadingClick"
    />
    <ClientTotal/>
    <Customertransaction
      :clients="clientStore.clients"
    />

    <ClientModal
      :open="showClientModal"
      :client="selectedClient"
      @close="closeModal"
      @client-saved="onClientSaved"
    />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useClientStore } from '~/stores/clients'
import { useToast } from 'vue-toastification'
import { ClientTotal } from '#components'

definePageMeta({
  middleware: 'auth'
})

const clientStore = useClientStore()
const toast = useToast()
const showClientModal = ref(false)
const selectedClient = ref(null)

function handleHeadingClick(action: string) {
  if (action === 'openClient') {
    selectedClient.value = null // Clear for new client
    showClientModal.value = true
  }
}

async function onClientSaved() {
  showClientModal.value = false
  selectedClient.value = null

  // Clear any search filters to show all clients
  clientStore.resetFilters()

  // Refresh the clients list and stats
  await clientStore.refreshClients()
}

function closeModal() {
  showClientModal.value = false
  selectedClient.value = null
}



// Load clients on component mount
onMounted(async () => {
  try {
    await clientStore.refreshClients()
  } catch (error: any) {
    toast.error('Failed to load clients: ' + error.message)
  }
})
</script>
