<template>
    <div>
      <!-- Heading -->
      <Heading
    title="Manage Projects"
    description="Manage your ongoing and upcoming projects"
    icon="lucide:file"
    iconColor="text-green-400"
    bgColor="bg-white"
    :buttons="[
      {
        label: 'New Projects',
        icon: 'lucide:circle-plus',
        bgColor: 'bg-[#00C951]',
        textColor: 'text-white',
        to: '/invoices/create'
      },
    ]"
  />
      <div class="px-4 lg:px-8">
  <!-- other contents goes here -->
        <div class="space-y-4 mt-4">
          <div 
            v-if="isLoading"
            class="p-8 w-full rounded-lg flex items-center justify-center bg-muted"
          >
            <Loader />
          </div>
  
          <Empty v-if="!messages.length && !isLoading" label="create your first billing here" />
  
          <div class="flex flex-col-reverse gap-y-4">
            <div 
              v-for="(message, i) in messages" 
              :key="i"
              :class="`p-8 w-full rounded-lg ${message.role === 'user' ? 'bg-white border border-black/10' : 'bg-slate-100'}`"
            >
              <UserAvatar v-if="message.role === 'user'" />
              <BotAvatar v-else />
              <p class="text-sm whitespace-pre-line">
                {{ message.content }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </template>
  
  <script lang="ts" setup>
  import type { ChatCompletionRequestMessage } from '~/utils/types';
  
  const prompt = ref('');
  const isLoading = ref(false);
  const messages = ref<ChatCompletionRequestMessage[]>([]);
  
  const submitForm = async () => {
    if (!prompt.value.trim()) return;
  
    isLoading.value = true;
  
    const userMessage: ChatCompletionRequestMessage = {
      role: 'user',
      content: prompt.value,
    };
  
    messages.value.push(userMessage);
    const userPrompt = prompt.value;
    prompt.value = '';
  
    // Simulate bot response after a delay
    setTimeout(() => {
      const botMessage: ChatCompletionRequestMessage = {
        role: 'assistant',
        content: `You asked: "${userPrompt}". Here's a simple explanation:\nTo calculate radius, use the formula:\nradius = diameter / 2.`,
      };
  
      messages.value.push(botMessage);
      isLoading.value = false;
    }, 1000);
  };
  </script>
  
  <style>
  /* Add any custom styles if needed */
  </style>
  