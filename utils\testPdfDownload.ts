import type { Invoice } from '~/services/invoiceApi'
import { downloadInvoicePDFDirect } from './directPdfDownloader'

// Test invoice data for PDF generation testing
export const createTestInvoice = (): Invoice => {
  return {
    _id: 'test-invoice-001',
    invoiceNumber: 'INV-TEST-001',
    clientName: 'Test Client Company',
    clientEmail: '<EMAIL>',
    clientPhone: '+****************',
    clientAddress: {
      street: '123 Business Street',
      city: 'Test City',
      state: 'Test State',
      zipCode: '12345',
      country: 'Test Country'
    },
    issueDate: new Date().toISOString(),
    dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days from now
    invoiceType: 'product',
    status: 'sent',
    isPaid: false,
    items: [
      {
        _id: 'item-1',
        description: 'Professional Web Development Services',
        type: 'service',
        quantity: 40,
        unitPrice: 75.00,
        total: 3000.00
      },
      {
        _id: 'item-2',
        description: 'Domain Registration and Hosting Setup',
        type: 'service',
        quantity: 1,
        unitPrice: 150.00,
        total: 150.00
      },
      {
        _id: 'item-3',
        description: 'SSL Certificate Installation',
        type: 'service',
        quantity: 1,
        unitPrice: 50.00,
        total: 50.00
      }
    ],
    subtotal: 3200.00,
    discountRate: 5,
    discountAmount: 160.00,
    taxRate: 8.5,
    taxAmount: 258.40,
    total: 3298.40,
    notes: 'Thank you for your business! Payment is due within 30 days of invoice date. Please include invoice number with payment.',
    paymentTerms: 'Net 30',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    userId: 'test-user-id'
  }
}

// Test function to verify PDF download functionality
export const testPDFDownload = async (): Promise<boolean> => {
  try {
    console.log('🧪 Testing PDF download functionality...')
    
    // Create test invoice
    const testInvoice = createTestInvoice()
    
    // Test PDF generation
    await downloadInvoicePDFDirect(testInvoice, {
      filename: 'TEST-Invoice-Download.pdf',
      format: 'a4',
      orientation: 'portrait'
    })
    
    console.log('✅ PDF download test completed successfully!')
    console.log('📄 Check your Downloads folder for: TEST-Invoice-Download.pdf')
    
    return true
  } catch (error) {
    console.error('❌ PDF download test failed:', error)
    return false
  }
}

// Browser console helper function
export const runPDFTest = () => {
  if (typeof window !== 'undefined') {
    testPDFDownload().then(success => {
      if (success) {
        console.log('🎉 PDF Download Test: PASSED')
      } else {
        console.log('💥 PDF Download Test: FAILED')
      }
    })
  } else {
    console.log('⚠️ PDF test can only be run in browser environment')
  }
}

// Make test function available globally for browser console testing
if (typeof window !== 'undefined') {
  (window as any).testPDFDownload = runPDFTest
  console.log('🔧 PDF Test Function Available: Run testPDFDownload() in browser console')
}

export default {
  createTestInvoice,
  testPDFDownload,
  runPDFTest
}
