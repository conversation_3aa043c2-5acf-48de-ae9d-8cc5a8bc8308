const mongoose = require('mongoose');

const productSchema = new mongoose.Schema({
  userId: {
    type: String,
    required: [true, 'User ID is required'],
    index: true
  },
  productId: {
    type: String,
    unique: true,
    index: true
  },
  name: {
    type: String,
    required: [true, 'Product name is required'],
    trim: true,
    maxlength: [100, 'Product name cannot exceed 100 characters']
  },
  description: {
    type: String,
    trim: true,
    maxlength: [1000, 'Description cannot exceed 1000 characters']
  },
  category: {
    type: String,
    required: [true, 'Category is required'],
    enum: [
      'Electronics',
      'Clothing',
      'Books',
      'Home & Garden',
      'Sports & Outdoors',
      'Health & Beauty',
      'Automotive',
      'Office Supplies',
      'Software',
      'Digital Products',
      'Other'
    ],
    default: 'Other'
  },
  sku: {
    type: String,
    trim: true,
    unique: true,
    sparse: true,
    maxlength: [50, 'SKU cannot exceed 50 characters']
  },
  barcode: {
    type: String,
    trim: true,
    maxlength: [50, 'Barcode cannot exceed 50 characters']
  },
  // Pricing
  costPrice: {
    type: Number,
    required: [true, 'Cost price is required'],
    min: [0, 'Cost price cannot be negative']
  },
  sellingPrice: {
    type: Number,
    required: [true, 'Selling price is required'],
    min: [0, 'Selling price cannot be negative']
  },
  currency: {
    type: String,
    default: 'USD',
    enum: ['USD', 'EUR', 'GBP', 'CAD', 'AUD', 'INR', 'JPY'],
    uppercase: true
  },
  // Inventory
  stock: {
    type: Number,
    required: [true, 'Stock quantity is required'],
    min: [0, 'Stock cannot be negative'],
    default: 0
  },
  minStockLevel: {
    type: Number,
    default: 5,
    min: [0, 'Minimum stock level cannot be negative']
  },
  maxStockLevel: {
    type: Number,
    min: [0, 'Maximum stock level cannot be negative']
  },
  unit: {
    type: String,
    required: [true, 'Unit is required'],
    enum: ['piece', 'kg', 'gram', 'liter', 'meter', 'box', 'pack', 'dozen', 'other'],
    default: 'piece'
  },
  // Tax
  taxRate: {
    type: Number,
    default: 0,
    min: [0, 'Tax rate cannot be negative'],
    max: [100, 'Tax rate cannot exceed 100%']
  },
  taxInclusive: {
    type: Boolean,
    default: false
  },
  // Status and visibility
  status: {
    type: String,
    enum: ['active', 'inactive', 'discontinued'],
    default: 'active'
  },
  isVisible: {
    type: Boolean,
    default: true
  },
  // Images and media
  images: [{
    url: {
      type: String,
      trim: true
    },
    alt: {
      type: String,
      trim: true
    },
    isPrimary: {
      type: Boolean,
      default: false
    }
  }],
  // Dimensions and weight
  dimensions: {
    length: {
      type: Number,
      min: 0
    },
    width: {
      type: Number,
      min: 0
    },
    height: {
      type: Number,
      min: 0
    },
    unit: {
      type: String,
      enum: ['cm', 'inch', 'mm'],
      default: 'cm'
    }
  },
  weight: {
    value: {
      type: Number,
      min: 0
    },
    unit: {
      type: String,
      enum: ['kg', 'gram', 'pound', 'ounce'],
      default: 'kg'
    }
  },
  // Additional fields
  tags: [{
    type: String,
    trim: true,
    maxlength: [30, 'Tag cannot exceed 30 characters']
  }],
  notes: {
    type: String,
    trim: true,
    maxlength: [1000, 'Notes cannot exceed 1000 characters']
  },
  // Sales tracking
  totalSold: {
    type: Number,
    default: 0,
    min: 0
  },
  totalRevenue: {
    type: Number,
    default: 0,
    min: 0
  },
  lastSoldDate: {
    type: Date
  }
}, {
  timestamps: true
});

// Pre-save middleware to generate product ID
productSchema.pre('save', function(next) {
  if (!this.productId) {
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    this.productId = `P-${year}${month}-${random}`;
  }
  
  // Ensure only one primary image
  const primaryImages = this.images.filter(img => img.isPrimary);
  if (primaryImages.length > 1) {
    this.images.forEach((img, index) => {
      img.isPrimary = index === 0;
    });
  }
  
  next();
});

// Virtual for profit margin
productSchema.virtual('profitMargin').get(function() {
  if (this.sellingPrice > 0) {
    return ((this.sellingPrice - this.costPrice) / this.sellingPrice * 100).toFixed(2);
  }
  return 0;
});

// Virtual for low stock alert
productSchema.virtual('isLowStock').get(function() {
  return this.stock <= this.minStockLevel;
});

// Static method to find products by user
productSchema.statics.findByUser = function(userId, options = {}) {
  const { search, category, status, sort = '-createdAt', limit = 10, skip = 0, lowStock } = options;
  
  let query = { userId };
  
  if (status && status !== 'all') {
    query.status = status;
  }
  
  if (category && category !== 'all') {
    query.category = category;
  }
  
  if (search) {
    query.$or = [
      { name: { $regex: search, $options: 'i' } },
      { description: { $regex: search, $options: 'i' } },
      { productId: { $regex: search, $options: 'i' } },
      { sku: { $regex: search, $options: 'i' } }
    ];
  }
  
  if (lowStock) {
    query.$expr = { $lte: ['$stock', '$minStockLevel'] };
  }
  
  return this.find(query)
    .sort(sort)
    .limit(limit)
    .skip(skip);
};

// Static method to get product statistics
productSchema.statics.getStats = function(userId) {
  return this.aggregate([
    { $match: { userId } },
    {
      $group: {
        _id: null,
        totalProducts: { $sum: 1 },
        activeProducts: {
          $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] }
        },
        inactiveProducts: {
          $sum: { $cond: [{ $eq: ['$status', 'inactive'] }, 1, 0] }
        },
        lowStockProducts: {
          $sum: { $cond: [{ $lte: ['$stock', '$minStockLevel'] }, 1, 0] }
        },
        totalStock: { $sum: '$stock' },
        totalValue: { $sum: { $multiply: ['$stock', '$costPrice'] } },
        totalRevenue: { $sum: '$totalRevenue' }
      }
    }
  ]);
};

module.exports = mongoose.model('Product', productSchema);
