<template>
  <div class="bg-white rounded-lg shadow-sm border p-6">
    <div class="flex justify-between items-center mb-6">
      <h2 class="text-xl font-semibold text-gray-900">
        {{ isEditing ? 'Edit Service' : 'Create New Service' }}
      </h2>
      <button
        @click="$emit('close')"
        class="text-gray-400 hover:text-gray-600"
      >
        <Icon name="lucide:x" class="w-6 h-6" />
      </button>
    </div>

    <form @submit.prevent="handleSubmit" class="space-y-6">
      <!-- Basic Information -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Service ID
          </label>
          <input
            v-model="form.serviceId"
            type="text"
            class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50"
            placeholder="Auto-generated if empty"
            readonly
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Service Name *
          </label>
          <input
            v-model="form.name"
            type="text"
            required
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#00C951] focus:border-transparent"
            placeholder="Service name"
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Category *
          </label>
          <select
            v-model="form.category"
            required
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#00C951] focus:border-transparent"
          >
            <option value="">Select category</option>
            <option
              v-for="category in serviceCategories"
              :key="category"
              :value="category"
            >
              {{ category }}
            </option>
          </select>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Pricing Type *
          </label>
          <select
            v-model="form.pricingType"
            required
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#00C951] focus:border-transparent"
          >
            <option value="">Select pricing type</option>
            <option value="hourly">Hourly</option>
            <option value="fixed">Fixed Price</option>
            <option value="daily">Daily</option>
            <option value="monthly">Monthly</option>
            <option value="project">Per Project</option>
          </select>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Price *
          </label>
          <input
            v-model.number="form.price"
            type="number"
            step="0.01"
            min="0"
            required
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#00C951] focus:border-transparent"
            placeholder="0.00"
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Tax Rate (%)
          </label>
          <input
            v-model.number="form.taxRate"
            type="number"
            min="0"
            max="100"
            step="0.01"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#00C951] focus:border-transparent"
            placeholder="0"
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Duration (if applicable)
          </label>
          <input
            v-model="form.duration"
            type="text"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#00C951] focus:border-transparent"
            placeholder="e.g., 2 hours, 1 day, 1 week"
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Status
          </label>
          <select
            v-model="form.status"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#00C951] focus:border-transparent"
          >
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
          </select>
        </div>
      </div>

      <!-- Availability Settings -->
      <div class="border-t pt-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Availability Settings</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="flex items-center space-x-2">
              <input
                v-model="form.isAvailable"
                type="checkbox"
                class="rounded border-gray-300 text-[#00C951] focus:ring-[#00C951]"
              />
              <span class="text-sm font-medium text-gray-700">Currently Available</span>
            </label>
          </div>

          <div>
            <label class="flex items-center space-x-2">
              <input
                v-model="form.requiresBooking"
                type="checkbox"
                class="rounded border-gray-300 text-[#00C951] focus:ring-[#00C951]"
              />
              <span class="text-sm font-medium text-gray-700">Requires Advance Booking</span>
            </label>
          </div>

          <div v-if="form.requiresBooking">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              Minimum Booking Notice (hours)
            </label>
            <input
              v-model.number="form.minBookingNotice"
              type="number"
              min="0"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#00C951] focus:border-transparent"
              placeholder="24"
            />
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              Maximum Concurrent Bookings
            </label>
            <input
              v-model.number="form.maxConcurrentBookings"
              type="number"
              min="1"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#00C951] focus:border-transparent"
              placeholder="1"
            />
          </div>
        </div>
      </div>

      <!-- Description -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">
          Description
        </label>
        <textarea
          v-model="form.description"
          rows="4"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#00C951] focus:border-transparent"
          placeholder="Service description..."
        ></textarea>
      </div>

      <!-- Service Features -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">
          Features/Inclusions
        </label>
        <div class="space-y-2">
          <div
            v-for="(feature, index) in form.features"
            :key="index"
            class="flex items-center space-x-2"
          >
            <input
              v-model="form.features[index]"
              type="text"
              class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#00C951] focus:border-transparent"
              placeholder="Feature description"
            />
            <button
              type="button"
              @click="removeFeature(index)"
              class="text-red-600 hover:text-red-800 p-2"
            >
              <Icon name="lucide:trash-2" class="w-4 h-4" />
            </button>
          </div>
          <button
            type="button"
            @click="addFeature"
            class="text-[#00C951] hover:text-[#00B847] text-sm font-medium"
          >
            + Add Feature
          </button>
        </div>
      </div>

      <!-- Pricing Summary -->
      <div class="bg-gray-50 p-4 rounded-md">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <span class="text-sm text-gray-600">Base Price:</span>
            <div class="font-medium">${{ form.price.toFixed(2) }}</div>
          </div>
          <div>
            <span class="text-sm text-gray-600">Tax ({{ form.taxRate }}%):</span>
            <div class="font-medium">${{ taxAmount.toFixed(2) }}</div>
          </div>
          <div>
            <span class="text-sm text-gray-600">Total Price:</span>
            <div class="font-bold text-lg">${{ totalPrice.toFixed(2) }}</div>
          </div>
        </div>
      </div>

      <!-- Form Actions -->
      <div class="flex justify-end space-x-4 pt-6 border-t">
        <button
          type="button"
          @click="$emit('close')"
          class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
        >
          Cancel
        </button>
        <button
          type="submit"
          :disabled="isSubmitting"
          class="px-6 py-2 bg-[#00C951] text-white rounded-md hover:bg-[#00B847] transition-colors disabled:opacity-50"
        >
          {{ isSubmitting ? 'Saving...' : (isEditing ? 'Update Service' : 'Create Service') }}
        </button>
      </div>
    </form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useServicesStore } from '~/stores/services'
import type { Service } from '~/services/serviceApi'

interface Props {
  service?: Service | null
}

const props = defineProps<Props>()
const emit = defineEmits<{
  close: []
  success: [service: Service]
}>()

const servicesStore = useServicesStore()

const isSubmitting = ref(false)
const isEditing = computed(() => !!props.service)

// Service categories
const serviceCategories = [
  'Consulting',
  'Development',
  'Design',
  'Marketing',
  'Support',
  'Training',
  'Maintenance',
  'Installation',
  'Repair',
  'Cleaning',
  'Transportation',
  'Other'
]

// Form data
const form = reactive({
  serviceId: '',
  name: '',
  category: '',
  pricingType: '',
  price: 0,
  taxRate: 0,
  duration: '',
  status: 'active' as 'active' | 'inactive',
  isAvailable: true,
  requiresBooking: false,
  minBookingNotice: 24,
  maxConcurrentBookings: 1,
  description: '',
  features: ['']
})

// Computed values
const taxAmount = computed(() => (form.price * form.taxRate) / 100)
const totalPrice = computed(() => form.price + taxAmount.value)

// Load data on mount
onMounted(async () => {
  try {
    // If editing, populate form with service data
    if (props.service) {
      populateForm(props.service)
    }
  } catch (error) {
    console.error('Failed to load service data:', error)
  }
})

// Populate form with service data for editing
const populateForm = (service: Service) => {
  form.serviceId = service.serviceId
  form.name = service.name
  form.category = service.category
  form.pricingType = service.pricingType
  form.price = service.price
  form.taxRate = service.taxRate
  form.duration = service.duration || ''
  form.status = service.status
  form.isAvailable = service.isAvailable
  form.requiresBooking = service.requiresBooking
  form.minBookingNotice = service.minBookingNotice
  form.maxConcurrentBookings = service.maxConcurrentBookings
  form.description = service.description || ''
  form.features = service.features?.length ? [...service.features] : ['']
}

// Add feature
const addFeature = () => {
  form.features.push('')
}

// Remove feature
const removeFeature = (index: number) => {
  if (form.features.length > 1) {
    form.features.splice(index, 1)
  }
}

// Handle form submission
const handleSubmit = async () => {
  try {
    isSubmitting.value = true
    
    const serviceData = {
      ...form,
      features: form.features.filter(f => f.trim() !== '')
    }
    
    let result
    if (isEditing.value && props.service) {
      result = await servicesStore.updateService(props.service._id, serviceData)
    } else {
      result = await servicesStore.createService(serviceData)
    }
    
    emit('success', result)
    emit('close')
  } catch (error) {
    console.error('Failed to save service:', error)
  } finally {
    isSubmitting.value = false
  }
}
</script>
