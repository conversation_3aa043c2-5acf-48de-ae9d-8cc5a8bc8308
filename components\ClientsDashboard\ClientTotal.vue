<template>
  <div class="space-y-6">
    <!-- Client Statistics Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
      <!-- Total Clients Card -->
      <div class="bg-white text-black rounded-xl p-3 flex justify-between items-center shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200">
        <div class="space-y-2">
          <h2 class="text-gray-600 text-sm font-semibold">Total Clients</h2>
          <div class="text-3xl font-bold text-[#00C951]">{{ totalClientsCount }}</div>
          <p class="text-sm text-gray-400">All Clients</p>
        </div>
        <div class="p-3 bg-green-100 rounded-xl">
          <Icon name="lucide:users" class="w-8 h-8 text-[#00C951]"/>
        </div>
      </div>

      <!-- Business Clients -->
      <BusinessTotal/>

      <!-- Individual Clients -->
      <IndividualTotal/>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useClientStore } from '~/stores/clients'

const clientStore = useClientStore()

const totalClientsCount = computed(() => {
  return clientStore.clients.length
})
</script>
  