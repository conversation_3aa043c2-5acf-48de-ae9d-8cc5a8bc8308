<template>
    <div class="h-full flex flex-col gap-y-4 items-center justify-center">
      <div class="w-10 h-10 relative">
        <img src="/assets/load.gif" alt="logo" />
      </div>
      <p class="text-sm text-muted-foreground" v-if="processing">Invoice Loading...</p>
    </div>
  </template>
  
  <script lang="ts" setup>
  interface Props {
    processing?: boolean;
  }
  
  // Use defineProps with default values
  const props = withDefaults(defineProps<Props>(), {
    processing: true,
  });
  </script>
  
  <style scoped>
  /* Add your styles here if needed */
  </style>
  